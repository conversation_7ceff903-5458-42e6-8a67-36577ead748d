{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/header.tsx"], "sourcesContent": ["import Image from 'next/image'\r\nimport React from 'react'\r\n\r\nconst Header = () => {\r\n  return (\r\n    <div className='w-full h-20 sticky top-0 flex justify-center items-center'><Image src={'/Logo.svg'} alt={''} width={200} height={100} /></div>\r\n  )\r\n}\r\n\r\nexport default Header"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAI,WAAU;kBAA4D,cAAA,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAa,KAAK;YAAI,OAAO;YAAK,QAAQ;;;;;;;;;;;AAErI;uCAEe"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/cart.jsx"], "sourcesContent": ["// \"use client\";\r\n// import React, { useRef, useState, useEffect } from \"react\";\r\n// import { Typography, Box, Divider, List } from \"@mui/material\";\r\n// import Header from \"./header\";\r\n\r\n// interface ROI {\r\n//   x: number;\r\n//   y: number;\r\n//   width: number;\r\n//   height: number;\r\n// }\r\n// interface CartProps {\r\n//   onUpdateTotalPrice: (price: number) => void;\r\n// }\r\n\r\n// const Cart: React.FC<CartProps> = ({ onUpdateTotalPrice }) => {\r\n//   const canvasRef = useRef<HTMLCanvasElement | null>(null);\r\n\r\n//   const [roi] = useState<ROI>({\r\n//     x: 208,\r\n//     y: 175,\r\n//     width: 465 - 208,\r\n//     height: 512 - 175,\r\n//   });\r\n\r\n//   const [cartItems, setCartItems] = useState<{ name: string; price: number }[]>(\r\n//     []\r\n//   );\r\n//   useEffect(() => {\r\n//     const totalAmount = cartItems.reduce((acc, item) => acc + item.price, 0);\r\n//     onUpdateTotalPrice(totalAmount); // Notify parent of the updated total price\r\n//   }, [cartItems, onUpdateTotalPrice]);\r\n\r\n//   // Fetch price based on weight_flag and productName\r\n//   const fetchProductPrice = async (productName: string, weightFlag: number) => {\r\n//     const endpoint = weightFlag === 0 ? \"pricePer\" : \"pricePerKg\";\r\n//     try {\r\n//       const response = await fetch(\r\n//         `http://192.168.1.26:5000/${endpoint}?productName=${encodeURIComponent(\r\n//           productName\r\n//         )}`\r\n//       );\r\n\r\n//       if (response.ok) {\r\n//         const data = await response.json();\r\n//         return weightFlag === 0 ? data.pricePer : data.pricePerKg;\r\n//       } else {\r\n//         console.error(`Failed to fetch price for ${productName}`);\r\n//         return 0;\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Error fetching product price:\", error);\r\n//       return 0;\r\n//     }\r\n//   };\r\n\r\n//   // Call predict API and update cart items\r\n//   const handleMotionDetection = async () => {\r\n//     try {\r\n//       if (!canvasRef.current) return;\r\n\r\n//       const canvas = canvasRef.current;\r\n//       const blob = await new Promise<Blob | null>((resolve) =>\r\n//         canvas.toBlob(resolve, \"image/png\")\r\n//       );\r\n//       if (!blob) return;\r\n\r\n//       const formData = new FormData();\r\n//       formData.append(\"image\", blob, \"capture.png\");\r\n\r\n//       const response = await fetch(\"http://192.168.1.6:9000/predict\", {\r\n//         method: \"POST\",\r\n//         body: formData,\r\n//       });\r\n\r\n//       if (response.ok) {\r\n//         const data = await response.json();\r\n//         console.log(\"Predict API Response:\", data);\r\n\r\n//         const { yolo_tag, weight_flag } = data;\r\n\r\n//         // Fetch prices for each product in yolo_tag\r\n//         const updatedCartItems = await Promise.all(\r\n//           yolo_tag.map(async (productName: string) => {\r\n//             const price = await fetchProductPrice(productName, weight_flag);\r\n//             return { name: productName, price };\r\n//           })\r\n//         );\r\n\r\n//         setCartItems(updatedCartItems);\r\n//       } else {\r\n//         console.error(\"Failed to fetch prediction data from API\");\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Error during motion handling:\", error);\r\n//     }\r\n//   };\r\n\r\n//   // Motion detection logic\r\n//   useEffect(() => {\r\n//     const cellSize = 30;\r\n//     const diffThreshold = 10000;\r\n//     const processEveryNthFrame = 3;\r\n//     let frameCounter = 0;\r\n//     let previousGrayData: Uint8ClampedArray | null = null;\r\n\r\n//     const fetchAndProcessImage = async () => {\r\n//       try {\r\n//         const response = await fetch(\"http://192.168.1.50:9000/image\");\r\n//         console.log(\"response\", response);\r\n//         const blob = await response.blob();\r\n//         const url = URL.createObjectURL(blob);\r\n//         const image = new Image();\r\n//         console.log(\"image function call\");\r\n//         image.src = url;\r\n//         console.log(image.src);\r\n//         image.onload = () => {\r\n//           console.log(onload);\r\n//           if (canvasRef.current) {\r\n//             const canvas = canvasRef.current;\r\n//             const ctx = canvas.getContext(\"2d\");\r\n//             if (!ctx) return;\r\n\r\n//             canvas.width = image.width;\r\n//             canvas.height = image.height;\r\n\r\n//             ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n//             ctx.drawImage(image, 0, 0);\r\n\r\n//             frameCounter++;\r\n//             if (frameCounter % processEveryNthFrame !== 0) {\r\n//               URL.revokeObjectURL(url);\r\n//               return;\r\n//             }\r\n\r\n//             const { x: roiX, y: roiY, width, height } = roi;\r\n//             const currentImageData = ctx.getImageData(\r\n//               roiX,\r\n//               roiY,\r\n//               width,\r\n//               height\r\n//             );\r\n//             const currentGrayData = toGrayscale(currentImageData);\r\n\r\n//             if (previousGrayData) {\r\n//               const motionCells = detectMotion(\r\n//                 previousGrayData,\r\n//                 currentGrayData,\r\n//                 width,\r\n//                 height,\r\n//                 cellSize,\r\n//                 diffThreshold\r\n//               );\r\n//               console.log(\"motion length\", motionCells.length);\r\n//               if (motionCells.length > 0) {\r\n//                 handleMotionDetection(); // Call predict API on motion detection\r\n//               }\r\n//             }\r\n\r\n//             previousGrayData = currentGrayData;\r\n//             URL.revokeObjectURL(url);\r\n//           }\r\n//         };\r\n//       } catch (error) {\r\n//         console.error(\"Error fetching or processing image:\", error);\r\n//       }\r\n//     };\r\n\r\n//     const interval = setInterval(fetchAndProcessImage, 250);\r\n\r\n//     return () => clearInterval(interval);\r\n//   }, [roi]);\r\n\r\n//   const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {\r\n//     const { data, width, height } = imageData;\r\n//     const grayData = new Uint8ClampedArray(width * height);\r\n//     for (let i = 0; i < data.length; i += 4) {\r\n//       const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\r\n//       grayData[i / 4] = gray;\r\n//     }\r\n//     return grayData;\r\n//   };\r\n\r\n//   const detectMotion = (\r\n//     prevGray: Uint8ClampedArray,\r\n//     currentGray: Uint8ClampedArray,\r\n//     width: number,\r\n//     height: number,\r\n//     cellSize: number,\r\n//     threshold: number\r\n//   ): { x: number; y: number }[] => {\r\n//     const motionCells: { x: number; y: number }[] = [];\r\n//     for (let y = 0; y <= height - cellSize; y += cellSize) {\r\n//       for (let x = 0; x <= width - cellSize; x += cellSize) {\r\n//         let cellDiff = 0;\r\n//         for (let i = y; i < y + cellSize; i++) {\r\n//           for (let j = x; j < x + cellSize; j++) {\r\n//             const index = i * width + j;\r\n//             if (index < prevGray.length) {\r\n//               cellDiff += Math.abs(prevGray[index] - currentGray[index]);\r\n//             }\r\n//           }\r\n//         }\r\n//         if (cellDiff > threshold) {\r\n//           motionCells.push({ x, y });\r\n//         }\r\n//       }\r\n//     }\r\n//     return motionCells;\r\n//   };\r\n\r\n//   const totalAmount = cartItems\r\n//     .reduce((acc, item) => acc + item.price, 0)\r\n//     .toFixed(2);\r\n\r\n//   return (\r\n//     <div className=\"h-full bg-white p-1 flex flex-col items-center justify-center\">\r\n//       <Header />\r\n\r\n//       <Box\r\n//         sx={{\r\n//           width: \"100%\",\r\n//           maxWidth: \"600px\",\r\n//           backgroundColor: \"white\",\r\n//           borderRadius: \"18px\",\r\n//           height: \"90%\",\r\n//           boxShadow: 3,\r\n//           p: 4,\r\n//           display: \"flex\",\r\n//           flexDirection: \"column\",\r\n//         }}\r\n//       >\r\n//         <Box\r\n//           display=\"flex\"\r\n//           justifyContent=\"space-between\"\r\n//           alignItems=\"center\"\r\n//           mb={2}\r\n//         >\r\n//           <Typography\r\n//             variant=\"h5\"\r\n//             component=\"h1\"\r\n//             className=\"text-black font-semibold\"\r\n//           >\r\n//             Your Cart\r\n//           </Typography>\r\n//         </Box>\r\n\r\n//         <List sx={{ flexGrow: 1, overflowY: \"auto\" }}>\r\n//           {cartItems.map((item, index) => (\r\n//             <div\r\n//               key={index}\r\n//               className=\"bg-gray-100 rounded-md shadow-md p-6 mb-2 flex justify-between items-center\"\r\n//             >\r\n//               <Typography variant=\"body1\" fontWeight=\"medium\">\r\n//                 {item.name}\r\n//               </Typography>\r\n//               <Typography className=\"flex items-center space-x-4\" component=\"div\">\r\n\r\n//                 <Typography\r\n//                 component=\"span\"\r\n//                   variant=\"body1\"\r\n//                   fontWeight=\"medium\"\r\n//                   className=\"text-right\"\r\n//                 >\r\n//                   ₹{item.price.toFixed(2)}\r\n//                   <Typography variant=\"body2\" color=\"textSecondary\">\r\n//                     x1\r\n//                   </Typography>\r\n//                 </Typography>\r\n//               </Typography>\r\n//             </div>\r\n//           ))}\r\n//         </List>\r\n\r\n//         <Divider sx={{ my: 2 }} />\r\n//         <Box display=\"flex\" justifyContent=\"space-between\" mb={2}>\r\n//           <Typography variant=\"h6\" fontWeight=\"bold\">\r\n//             Total:\r\n//           </Typography>\r\n//           <Typography variant=\"h6\" fontWeight=\"bold\">\r\n//             ₹{totalAmount}\r\n//           </Typography>\r\n//         </Box>\r\n//       </Box>\r\n//       <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n//       <div className=\" mt-2 text-sm -mb-2\"> Powered by Synecx AI</div>\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// export default Cart;\r\n\r\n// \"use client\";\r\n// import React, { useRef, useState, useEffect } from \"react\";\r\n// import { Typography, Box, Divider, List } from \"@mui/material\";\r\n// import Header from \"./header\";\r\n\r\n// interface ROI {\r\n//   x: number;\r\n//   y: number;\r\n//   width: number;\r\n//   height: number;\r\n// }\r\n// interface CartProps {\r\n//   onUpdateTotalPrice: (price: number) => void;\r\n// }\r\n\r\n// const Cart: React.FC<CartProps> = ({ onUpdateTotalPrice }) => {\r\n//   const canvasRef = useRef<HTMLCanvasElement | null>(null);\r\n\r\n//   const [roi] = useState<ROI>({\r\n//     x: 208,\r\n//     y: 175,\r\n//     width: 465 - 208,\r\n//     height: 512 - 175,\r\n//   });\r\n\r\n//   const [cartItems, setCartItems] = useState<{ name: string; price: number }[]>(\r\n//     []\r\n//   );\r\n  // useEffect(() => {\r\n  //   const totalAmount = cartItems.reduce((acc, item) => acc + item.price, 0);\r\n  //   onUpdateTotalPrice(totalAmount); // Notify parent of the updated total price\r\n  // }, [cartItems, onUpdateTotalPrice]);\r\n\r\n//   // Fetch price based on weight_flag and productName\r\n//   const fetchProductPrice = async (productName: string, weightFlag: number) => {\r\n//     const endpoint = weightFlag === 0 ? \"pricePer\" : \"pricePerKg\";\r\n//     try {\r\n//       const response = await fetch(\r\n//         `http://192.168.1.26:5000/${endpoint}?productName=${encodeURIComponent(\r\n//           productName\r\n//         )}`\r\n//       );\r\n\r\n//       if (response.ok) {\r\n//         const data = await response.json();\r\n//         return weightFlag === 0 ? data.pricePer : data.pricePerKg;\r\n//       } else {\r\n//         console.error(`Failed to fetch price for ${productName}`);\r\n//         return 0;\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Error fetching product price:\", error);\r\n//       return 0;\r\n//     }\r\n//   };\r\n\r\n//   // Call predict API and update cart items\r\n//   const handleMotionDetection = async () => {\r\n//     try {\r\n//       if (!canvasRef.current) return;\r\n\r\n//       const canvas = canvasRef.current;\r\n//       const blob = await new Promise<Blob | null>((resolve) =>\r\n//         canvas.toBlob(resolve, \"image/png\")\r\n//       );\r\n//       if (!blob) return;\r\n\r\n//       const formData = new FormData();\r\n//       formData.append(\"image\", blob, \"capture.png\");\r\n\r\n//       const response = await fetch(\"http://***********:9000/predict\", {\r\n//         method: \"POST\",\r\n//         body: formData,\r\n//       });\r\n\r\n//       if (response.ok) {\r\n//         const data = await response.json();\r\n//         console.log(\"Predict API Response:\", data);\r\n\r\n//         const { yolo_tag,clip_tag, weight_flag } = data;\r\n\r\n//         // Fetch prices for each product in yolo_tag\r\n//         const updatedCartItems = await Promise.all(\r\n//           yolo_tag.map(async (productName: string) => {\r\n//             const price = await fetchProductPrice(productName, weight_flag);\r\n//             return { name: productName, price };\r\n//           })\r\n//         );\r\n\r\n//         setCartItems(updatedCartItems);\r\n//       } else {\r\n//         console.error(\"Failed to fetch prediction data from API\");\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Error during motion handling:\", error);\r\n//     }\r\n//   };\r\n\r\n//   // Motion detection logic\r\n//   useEffect(() => {\r\n//     const cellSize = 30;\r\n//     const diffThreshold = 10000;\r\n//     const processEveryNthFrame = 3;\r\n//     let frameCounter = 0;\r\n//     let previousGrayData: Uint8ClampedArray | null = null;\r\n\r\n//     const fetchAndProcessImage = async () => {\r\n//       try {\r\n//         const response = await fetch(\"http://192.168.1.50:9000/image\");\r\n//         console.log(\"response\", response);\r\n//         const blob = await response.blob();\r\n//         const url = URL.createObjectURL(blob);\r\n//         const image = new Image();\r\n//         console.log(\"image function call\");\r\n//         image.src = url;\r\n//         console.log(image.src);\r\n//         image.onload = () => {\r\n//           console.log(onload);\r\n//           if (canvasRef.current) {\r\n//             const canvas = canvasRef.current;\r\n//             const ctx = canvas.getContext(\"2d\");\r\n//             if (!ctx) return;\r\n\r\n//             canvas.width = image.width;\r\n//             canvas.height = image.height;\r\n\r\n//             ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n//             ctx.drawImage(image, 0, 0);\r\n\r\n//             frameCounter++;\r\n//             if (frameCounter % processEveryNthFrame !== 0) {\r\n//               URL.revokeObjectURL(url);\r\n//               return;\r\n//             }\r\n\r\n//             const { x: roiX, y: roiY, width, height } = roi;\r\n//             const currentImageData = ctx.getImageData(\r\n//               roiX,\r\n//               roiY,\r\n//               width,\r\n//               height\r\n//             );\r\n//             const currentGrayData = toGrayscale(currentImageData);\r\n\r\n//             if (previousGrayData) {\r\n//               const motionCells = detectMotion(\r\n//                 previousGrayData,\r\n//                 currentGrayData,\r\n//                 width,\r\n//                 height,\r\n//                 cellSize,\r\n//                 diffThreshold\r\n//               );\r\n//               console.log(\"motion length\", motionCells.length);\r\n//               if (motionCells.length > 0) {\r\n//                 handleMotionDetection(); // Call predict API on motion detection\r\n//               }\r\n//             }\r\n\r\n//             previousGrayData = currentGrayData;\r\n//             URL.revokeObjectURL(url);\r\n//           }\r\n//         };\r\n//       } catch (error) {\r\n//         console.error(\"Error fetching or processing image:\", error);\r\n//       }\r\n//     };\r\n\r\n//     const interval = setInterval(fetchAndProcessImage, 250);\r\n\r\n//     return () => clearInterval(interval);\r\n//   }, [roi]);\r\n\r\n//   const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {\r\n//     const { data, width, height } = imageData;\r\n//     const grayData = new Uint8ClampedArray(width * height);\r\n//     for (let i = 0; i < data.length; i += 4) {\r\n//       const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\r\n//       grayData[i / 4] = gray;\r\n//     }\r\n//     return grayData;\r\n//   };\r\n\r\n//   const detectMotion = (\r\n//     prevGray: Uint8ClampedArray,\r\n//     currentGray: Uint8ClampedArray,\r\n//     width: number,\r\n//     height: number,\r\n//     cellSize: number,\r\n//     threshold: number\r\n//   ): { x: number; y: number }[] => {\r\n//     const motionCells: { x: number; y: number }[] = [];\r\n//     for (let y = 0; y <= height - cellSize; y += cellSize) {\r\n//       for (let x = 0; x <= width - cellSize; x += cellSize) {\r\n//         let cellDiff = 0;\r\n//         for (let i = y; i < y + cellSize; i++) {\r\n//           for (let j = x; j < x + cellSize; j++) {\r\n//             const index = i * width + j;\r\n//             if (index < prevGray.length) {\r\n//               cellDiff += Math.abs(prevGray[index] - currentGray[index]);\r\n//             }\r\n//           }\r\n//         }\r\n//         if (cellDiff > threshold) {\r\n//           motionCells.push({ x, y });\r\n//         }\r\n//       }\r\n//     }\r\n//     return motionCells;\r\n//   };\r\n\r\n//   const totalAmount = cartItems\r\n//     .reduce((acc, item) => acc + item.price, 0)\r\n//     .toFixed(2);\r\n\r\n//   return (\r\n//     <div className=\"h-full bg-white p-1 flex flex-col items-center justify-center\">\r\n//       <Header />\r\n\r\n//       <Box\r\n//         sx={{\r\n//           width: \"100%\",\r\n//           maxWidth: \"600px\",\r\n//           backgroundColor: \"white\",\r\n//           borderRadius: \"18px\",\r\n//           height: \"90%\",\r\n//           boxShadow: 3,\r\n//           p: 4,\r\n//           display: \"flex\",\r\n//           flexDirection: \"column\",\r\n//         }}\r\n//       >\r\n//         <Box\r\n//           display=\"flex\"\r\n//           justifyContent=\"space-between\"\r\n//           alignItems=\"center\"\r\n//           mb={2}\r\n//         >\r\n//           <Typography\r\n//             variant=\"h5\"\r\n//             component=\"h1\"\r\n//             className=\"text-black font-semibold\"\r\n//           >\r\n//             Your Cart\r\n//           </Typography>\r\n//         </Box>\r\n\r\n//         <List sx={{ flexGrow: 1, overflowY: \"auto\" }}>\r\n//           {cartItems.map((item, index) => (\r\n//             <div\r\n//               key={index}\r\n//               className=\"bg-gray-100 rounded-md shadow-md p-6 mb-2 flex justify-between items-center\"\r\n//             >\r\n//               <Typography variant=\"body1\" fontWeight=\"medium\">\r\n//                 {item.name}\r\n//               </Typography>\r\n//               <Typography className=\"flex items-center space-x-4\" component=\"div\">\r\n\r\n//                 <Typography\r\n//                 component=\"span\"\r\n//                   variant=\"body1\"\r\n//                   fontWeight=\"medium\"\r\n//                   className=\"text-right\"\r\n//                 >\r\n//                   ₹{item.price.toFixed(2)}\r\n//                   <Typography variant=\"body2\" color=\"textSecondary\">\r\n//                     x1\r\n//                   </Typography>\r\n//                 </Typography>\r\n//               </Typography>\r\n//             </div>\r\n//           ))}\r\n//         </List>\r\n\r\n//         <Divider sx={{ my: 2 }} />\r\n//         <Box display=\"flex\" justifyContent=\"space-between\" mb={2}>\r\n//           <Typography variant=\"h6\" fontWeight=\"bold\">\r\n//             Total:\r\n//           </Typography>\r\n//           <Typography variant=\"h6\" fontWeight=\"bold\">\r\n//             ₹{totalAmount}\r\n//           </Typography>\r\n//         </Box>\r\n//       </Box>\r\n//       <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n//       <div className=\" mt-2 text-sm -mb-2\"> Powered by Synecx AI</div>\r\n//     </div>\r\n//   );\r\n// };\r\n// export default Cart;\r\n\r\n\r\n\r\nimport React, { useState, useCallback, useEffect,useRef  } from \"react\";\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Box,\r\n  Button,\r\n  Dialog,\r\n  DialogActions,\r\n  DialogContent,\r\n  DialogTitle,\r\n  TextField,\r\n  List,\r\n  Divider,\r\n  ListItem,\r\n  FormControl,\r\n  MenuItem,\r\n  Select,\r\n  ListItemText,\r\n} from \"@mui/material\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport { toast } from \"react-toastify\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faShoppingCart } from \"@fortawesome/free-solid-svg-icons\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport SearchOutlinedIcon from \"@mui/icons-material/SearchOutlined\";\r\nimport AddOutlinedIcon from \"@mui/icons-material/AddOutlined\";\r\nimport Header from \"./header\";\r\n\r\nconst WEIGHT_BASED_ITEMS = new Set([\r\n  \"Milk Assorted\",\r\n  \"Normal Assorted\",\r\n  \"Ghee Assorted\",\r\n  \"Kaju Assorted\",\r\n  \"ARISI-MURUKKU\",\r\n  \"BOMBAY-MIXTURE\",\r\n  \"GARLIC-MIXTURE\",\r\n  \"KAARA-BOONTHI\",\r\n  \"KAARA-MURUKKU\",\r\n  \"KAI-SUTHU-MURUKKU\",\r\n  \"KARA-SEV\",\r\n  \"MASALA KADALAI\",\r\n  \"MASALA-POTATO-CHIPS-GREEN-\",\r\n  \"MASALA-POTATO-CHIPS-RED-\",\r\n  \"NAENDHRAM-CHIPS\",\r\n  \"NORMAL-MIXTURE\",\r\n  \"OTTU-PAKKODA\",\r\n  \"POTATO-CHIPS\",\r\n  \"PUDI-MURUKKU\",\r\n  \"THATTA-MURUKKU\",\r\n  \"CORN\",\r\n  \"BADHAM-BARFI\",\r\n  \"BADHUSHA\",\r\n  \"BANARAS-SANDWICH\",\r\n  \"BESAN-LADDU\",\r\n  \"BOMBAY-HALWA\",\r\n  \"CARROT-MYSORE-PAK\",\r\n  \"CHANDRAKALA\",\r\n  \"DRY-FRUIT-LADDU\",\r\n  \"GHEE-MYSORE-PAK\",\r\n  \"GULAB-JAMUN\",\r\n  \"GULKAN-BARFI\",\r\n  \"HORLICKS-BARFI\",\r\n  \"JILAPI\",\r\n  \"KAJU-KATLI\",\r\n  \"KAJU-PISTHA-ROLL\",\r\n  \"KALA-JAMUN\",\r\n  \"KALAKAN-BARFI\",\r\n  \"LADDU\",\r\n  \"LAMBA-JAMUN\",\r\n  \"MAKAN-PEDA\",\r\n  \"MANGO-KATLI\",\r\n  \"MILK-CAKE\",\r\n  \"MILK-PEDA\",\r\n  \"MOTHI-LADDU\",\r\n  \"MOTHI-PAK\",\r\n  \"MYSORE-PAK\",\r\n  \"RASGULLA\",\r\n  \"SPECIAL-GHEE-SOANPAPDI\",\r\n]);\r\n\r\n\r\nconst Cart = ({ onUpdateTotalPrice }) => {\r\n  const [items, setItems] = useState([]);\r\n  const [displayedItems, setDisplayedItems] = useState([]);\r\n  const [cartItems, setCartItems] = useState([]);\r\n  \r\n  const [openAddProductDialog, setOpenAddProductDialog] = useState(false);\r\n  const [newProductName, setNewProductName] = useState(\"\");\r\n  const [newProductPrice, setNewProductPrice] = useState(\"\");\r\n  const [newProductQuantity, setNewProductQuantity] = useState(1);\r\n  const [newProductShortcode, setNewProductShortcode] = useState(\"\");\r\n  const [weight, setWeight] = useState(0);\r\n  const [clicked, setClicked] = useState(false);\r\n  const [searchOpen, setSearchOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [openSearchDialog, setOpenSearchDialog] = useState(false);\r\n  const [clip_tag, setClipTag] = useState([]);\r\n\r\n  // Motion Detection State and Refs\r\n  const canvasRef = useRef(null);\r\n  const [roi, setRoi] = useState({ x: 210, y: 159, width: 257, height: 337 });\r\n  const [showMotionToast, setShowMotionToast] = useState(false);\r\n  const [showItemToast, setShowItemToast] = useState(false);\r\n  const motionDetectedRef = useRef(false);\r\n  const framesSinceMotionRef = useRef(0);\r\n  const cooldownRef = useRef(false);\r\n  const [itemPlaced, setItemPlaced] = useState(false); // New state for item placement\r\n\r\n// Calculate total price\r\nconst calculateTotalPrice = (items) => {\r\n  return items.reduce((acc, item) => {\r\n    if (item.weight_flag === 1) {\r\n      return acc + item.price * (item.weight || 0);\r\n    }\r\n    return acc + item.price * (item.quantity || 1);\r\n  }, 0);\r\n};\r\n\r\n// Update total price whenever cart items change\r\nuseEffect(() => {\r\n  const totalPrice = calculateTotalPrice(cartItems);\r\n  if (onUpdateTotalPrice) {\r\n    onUpdateTotalPrice(totalPrice);\r\n  }\r\n}, [cartItems, onUpdateTotalPrice]);\r\n\r\n  // useEffect(() => {\r\n  //   const cellSize = 30;\r\n  //   const diffThreshold = 10000;\r\n  //   const processEveryNthFrame = 3;\r\n  //   let frameCounter = 0;\r\n  //   let previousGrayData = null;\r\n\r\n  //   const fetchAndProcessImage = async () => {\r\n  //     try {\r\n  //       const response = await fetch(\"http://*************:9000/image\");\r\n  //       const blob = await response.blob();\r\n  //       const url = URL.createObjectURL(blob);\r\n  //       // const image = new Image();\r\n  //       const image = new window.Image();\r\n  //       image.src = url;\r\n  //       image.onload = () => {\r\n  //         const canvas = canvasRef.current;\r\n  //         const ctx = canvas.getContext(\"2d\");\r\n  //         canvas.width = image.width;\r\n  //         canvas.height = image.height;\r\n\r\n  //         ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n  //         ctx.drawImage(image, 0, 0);\r\n\r\n  //         frameCounter++;\r\n  //         if (frameCounter % processEveryNthFrame !== 0) {\r\n  //           URL.revokeObjectURL(url);\r\n  //           return;\r\n  //         }\r\n\r\n  //         const { x: roiX, y: roiY, width, height } = roi;\r\n\r\n  //         const currentImageData = ctx.getImageData(roiX, roiY, width, height);\r\n  //         const currentGrayData = toGrayscale(currentImageData);\r\n\r\n  //         if (previousGrayData) {\r\n  //           const motionCells = detectMotion(\r\n  //             previousGrayData,\r\n  //             currentGrayData,\r\n  //             width,\r\n  //             height,\r\n  //             cellSize,\r\n  //             diffThreshold\r\n  //           );\r\n\r\n  //           const motionDetectedNow = motionCells.length > 0;\r\n  //           if (motionDetectedNow && !cooldownRef.current) {\r\n  //             setShowMotionToast(true);\r\n  //             motionDetectedRef.current = true;\r\n  //             framesSinceMotionRef.current = 0;\r\n  //             cooldownRef.current = true;\r\n  //             setTimeout(() => {\r\n  //               setShowMotionToast(false);\r\n  //               cooldownRef.current = false;\r\n  //             }, 500);\r\n  //           } else if (motionDetectedRef.current) {\r\n  //             framesSinceMotionRef.current += 1;\r\n  //             if (framesSinceMotionRef.current >= 2) {\r\n  //               setShowItemToast(true);\r\n  //               motionDetectedRef.current = false;\r\n  //               framesSinceMotionRef.current = 0;\r\n  //               setItemPlaced(true); // Set itemPlaced to true\r\n  //               setTimeout(() => {\r\n  //                 setShowItemToast(false);\r\n  //               }, 1000);\r\n  //             }\r\n  //           }\r\n  //         }\r\n\r\n  //         previousGrayData = currentGrayData;\r\n  //         URL.revokeObjectURL(url);\r\n  //       };\r\n  //     } catch (error) {\r\n  //       console.error(\"Error fetching or processing image:\", error);\r\n  //     }\r\n  //   };\r\n\r\n  //   const interval = setInterval(fetchAndProcessImage, 250);\r\n  //   return () => clearInterval(interval);\r\n  // }, [roi]);\r\n\r\n\r\n //saas \r\n  useEffect(() => {\r\n    const cellSize = 20;\r\n    const diffThreshold = 12500;\r\n    const processEveryNthFrame = 1;\r\n    let frameCounter = 0;\r\n    let previousGrayData = null;\r\n\r\n  //   \r\n  const fetchAndProcessImage = async () => {\r\n    try {\r\n      const response = await fetch(\"http://*************:9000/image\");\r\n      const blob = await response.blob();\r\n      const url = URL.createObjectURL(blob);\r\n      // const image = new Image();\r\n      const image = new window.Image();\r\n      image.src = url;\r\n      image.onload = () => {\r\n        const canvas = canvasRef.current;\r\n        const ctx = canvas.getContext(\"2d\");\r\n        canvas.width = image.width;\r\n        canvas.height = image.height;\r\n\r\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n        ctx.drawImage(image, 0, 0);\r\n\r\n        frameCounter++;\r\n        if (frameCounter % processEveryNthFrame !== 0) {\r\n          URL.revokeObjectURL(url);\r\n          return;\r\n        }\r\n\r\n        const { x: roiX, y: roiY, width, height } = roi;\r\n\r\n        const currentImageData = ctx.getImageData(roiX, roiY, width, height);\r\n        const currentGrayData = toGrayscale(currentImageData);\r\n\r\n        if (previousGrayData) {\r\n          const motionCells = detectMotion(\r\n            previousGrayData,\r\n            currentGrayData,\r\n            width,\r\n            height,\r\n            cellSize,\r\n            diffThreshold\r\n          );\r\n\r\n          const motionDetectedNow = motionCells.length > 0;\r\n          if (motionDetectedNow && !cooldownRef.current) {\r\n            setShowMotionToast(true);\r\n            motionDetectedRef.current = true;\r\n            cooldownRef.current = true;\r\n\r\n            // Immediately call the prediction API\r\n            captureAndSendImage();\r\n\r\n            setTimeout(() => {\r\n              setShowMotionToast(false);\r\n              cooldownRef.current = false;\r\n            }, 300);\r\n          }\r\n        }\r\n\r\n        previousGrayData = currentGrayData;\r\n        URL.revokeObjectURL(url);\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching or processing image:\", error);\r\n    }\r\n  };\r\n\r\n  const interval = setInterval(fetchAndProcessImage, 250);\r\n  return () => clearInterval(interval);\r\n}, [roi]);\r\n\r\n  useEffect(() => {\r\n    if (itemPlaced) {\r\n      captureAndSendImage();\r\n      setItemPlaced(false); // Reset itemPlaced after prediction\r\n    }\r\n  }, [itemPlaced]);\r\n\r\n  const toGrayscale = (imageData) => {\r\n    const { data, width, height } = imageData;\r\n    const grayData = new Uint8ClampedArray(width * height);\r\n    for (let i = 0; i < data.length; i += 4) {\r\n      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\r\n      grayData[i / 4] = gray;\r\n    }\r\n    return grayData;\r\n  };\r\n\r\n  const detectMotion = (prevGray, currentGray, width, height, cellSize, threshold) => {\r\n    const motionCells = [];\r\n    for (let y = 0; y <= height - cellSize; y += cellSize) {\r\n      for (let x = 0; x <= width - cellSize; x += cellSize) {\r\n        let cellDiff = 0;\r\n        for (let i = y; i < y + cellSize; i++) {\r\n          for (let j = x; j < x + cellSize; j++) {\r\n            const index = i * width + j;\r\n            if (index < prevGray.length) {\r\n              cellDiff += Math.abs(prevGray[index] - currentGray[index]);\r\n            }\r\n          }\r\n        }\r\n        if (cellDiff > threshold) {\r\n          motionCells.push({ x, y });\r\n        }\r\n      }\r\n    }\r\n    return motionCells;\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const savedItems = localStorage.getItem(\"cartItems\");\r\n      setCartItems(savedItems ? JSON.parse(savedItems) : []);\r\n    }\r\n  }, []);\r\n  const subtotal = cartItems\r\n    .reduce((acc, item) => {\r\n      if (item.weight_flag === 1) {\r\n        return acc + item.price * (item.weight || 0);\r\n      }\r\n      return acc + item.price * (item.quantity || 1);\r\n    }, 0)\r\n    .toFixed(2);\r\n\r\n\r\n  const totalAmount = Math.round(parseFloat(subtotal));\r\n\r\n  const handleDropdownChange = async (index, newProductName) => {\r\n    try {\r\n      const isWeightBased = WEIGHT_BASED_ITEMS.has(newProductName);\r\n      const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n      const priceResponse = await fetch(\r\n        `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(\r\n          newProductName\r\n        )}`\r\n      );\r\n\r\n      if (!priceResponse.ok) {\r\n        throw new Error(\r\n          `Failed to fetch price for ${newProductName}: ${priceResponse.statusText}`\r\n        );\r\n      }\r\n\r\n      const priceData = await priceResponse.json();\r\n      const newPrice = isWeightBased\r\n        ? priceData.pricePerKg\r\n        : priceData.pricePer;\r\n\r\n      setCartItems((prevCartItems) => {\r\n        const updatedCartItems = prevCartItems.map((item, i) => {\r\n          if (i === index) {\r\n            const updatedItem = {\r\n              ...item,\r\n              selectedProduct: newProductName,\r\n              productName: newProductName,\r\n              price: newPrice,\r\n              weight_flag: isWeightBased ? 1 : 0,\r\n            };\r\n\r\n            if (item.weight_flag !== (isWeightBased ? 1 : 0)) {\r\n              if (isWeightBased) {\r\n                delete updatedItem.quantity;\r\n                updatedItem.weight = 0;\r\n              } else {\r\n                delete updatedItem.weight;\r\n                updatedItem.quantity = 1;\r\n              }\r\n            }\r\n\r\n            return updatedItem;\r\n          }\r\n          return item;\r\n        });\r\n\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error updating product price:\", error);\r\n      toast.error(\"Failed to update product price\");\r\n    }\r\n  };\r\n \r\n\r\n  // \r\n  // const captureAndSendImage = useCallback(async () => {\r\n  //   try {\r\n  //     toast.info(\"Capturing image...\", { autoClose: 800 });\r\n\r\n  //     const response = await fetch(\"http://*************:9000/image\");\r\n  //     if (!response.ok) {\r\n  //       throw new Error(`Image capture failed: ${response.statusText}`);\r\n  //     }\r\n  //     const blob = await response.blob();\r\n\r\n  //     const formData = new FormData();\r\n  //     formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n\r\n  //     toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n\r\n  //     const predictResponse = await fetch(\"https://shrew-golden-toucan.ngrok-free.app/predict\", {\r\n  //       method: \"POST\",\r\n  //       body: formData,\r\n  //     });\r\n\r\n  //     if (!predictResponse.ok) {\r\n  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n  //     }\r\n\r\n  //     const data = await predictResponse.json();\r\n  //     console.log(data);\r\n  //     const { yolo_tag, clip_tag, weight_flag } = data;\r\n\r\n  //     const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n  //     if (!weightResponse.ok) {\r\n  //       throw new Error(\"Failed to fetch weight\");\r\n  //     }\r\n  //     const weightData = await weightResponse.json();\r\n  //     const weight = weightData.weight;\r\n\r\n  //     setClipTag(clip_tag);\r\n  //     console.log(\"Clip Tag: \", clip_tag);\r\n\r\n  //     // Update cart items based on prediction\r\n  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n  //     const newCartItems = await Promise.all(\r\n  //       tags.map(async (productName, index) => {\r\n  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n  //         const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n  //         const priceResponse = await fetch(\r\n  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n  //         );\r\n\r\n  //         if (!priceResponse.ok) {\r\n  //           throw new Error(`Failed to fetch price for ${productName}`);\r\n  //         }\r\n\r\n  //         const priceData = await priceResponse.json();\r\n  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n\r\n  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n\r\n  //         return {\r\n  //           productName,\r\n  //           weight_flag: isWeightBased ? 1 : 0,\r\n  //           alternatives: [productName, ...alternatives],\r\n  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),\r\n  //         };\r\n  //       })\r\n  //     );\r\n\r\n  //     // Remove items not in the predict response\r\n  //     setCartItems((prevCartItems) => {\r\n  //       const updatedCartItems = prevCartItems.filter((item) =>\r\n  //         tags.includes(item.productName)\r\n  //       );\r\n\r\n  //       // Add new items from the predict response\r\n  //       newCartItems.forEach((newItem) => {\r\n  //         const existingItemIndex = updatedCartItems.findIndex(\r\n  //           (item) => item.productName === newItem.productName\r\n  //         );\r\n\r\n  //         if (existingItemIndex > -1) {\r\n  //           const existingItem = updatedCartItems[existingItemIndex];\r\n  //           if (newItem.weight_flag === 1) {\r\n  //             updatedCartItems[existingItemIndex] = {\r\n  //               ...existingItem,\r\n  //               weight: (existingItem.weight || 0) + (newItem.weight || 0),\r\n  //             };\r\n  //           } else {\r\n  //             updatedCartItems[existingItemIndex] = {\r\n  //               ...existingItem,\r\n  //               quantity: (existingItem.quantity || 1) + (newItem.quantity || 1),\r\n  //             };\r\n  //           }\r\n  //         } else {\r\n  //           updatedCartItems.push(newItem);\r\n  //         }\r\n  //       });\r\n\r\n  //       localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  //       return updatedCartItems;\r\n  //     });\r\n\r\n  //     setWeight(weight);\r\n  //   } catch (error) {\r\n  //     console.error(\"Error capturing or sending image:\", error);\r\n  //   }\r\n  // }, []);\r\n  \r\n\r\n\r\n\r\n  // const captureAndSendImage = useCallback(async () => {\r\n  //   try {\r\n  //     toast.info(\"Capturing image...\", { autoClose: 800 });\r\n  \r\n  //     const response = await fetch(\"http://*************:9000/image\");\r\n  //     if (!response.ok) {\r\n  //       throw new Error(`Image capture failed: ${response.statusText}`);\r\n  //     }\r\n  //     const blob = await response.blob();\r\n  \r\n  //     const formData = new FormData();\r\n  //     formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n  \r\n  //     toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n  \r\n  //     const predictResponse = await fetch(\"https://shrew-golden-toucan.ngrok-free.app/predict\", {\r\n  //       method: \"POST\",\r\n  //       body: formData,\r\n  //     });\r\n  \r\n  //     if (!predictResponse.ok) {\r\n  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n  //     }\r\n  \r\n  //     const data = await predictResponse.json();\r\n  //     console.log(data);\r\n  //     const { yolo_tag, clip_tag, weight_flag } = data;\r\n  \r\n  //     const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n  //     if (!weightResponse.ok) {\r\n  //       throw new Error(\"Failed to fetch weight\");\r\n  //     }\r\n  //     const weightData = await weightResponse.json();\r\n  //     const weight = weightData.weight;\r\n  \r\n  //     setClipTag(clip_tag);\r\n  //     console.log(\"Clip Tag: \", clip_tag);\r\n  \r\n  //     // Process predicted items\r\n  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n  //     const newCartItems = await Promise.all(\r\n  //       tags.map(async (productName, index) => {\r\n  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n  //         const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n  //         const priceResponse = await fetch(\r\n  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n  //         );\r\n  \r\n  //         if (!priceResponse.ok) {\r\n  //           throw new Error(`Failed to fetch price for ${productName}`);\r\n  //         }\r\n  \r\n  //         const priceData = await priceResponse.json();\r\n  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n  \r\n  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n  \r\n  //         return {\r\n  //           productName,\r\n  //           weight_flag: isWeightBased ? 1 : 0,\r\n  //           alternatives: [productName, ...alternatives],\r\n  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),\r\n  //           fromPredict: true  // Mark items from prediction\r\n  //         };\r\n  //       })\r\n  //     );\r\n  \r\n  //     // Update cart items\r\n  //     setCartItems((prevCartItems) => {\r\n  //       // Keep manually added items\r\n  //       const manualItems = prevCartItems.filter(item => !item.fromPredict);\r\n        \r\n  //       // Merge predicted items with existing quantities/weights\r\n  //       const predictedItems = newCartItems.map(newItem => {\r\n  //         const existingItem = prevCartItems.find(\r\n  //           item => item.productName === newItem.productName && item.fromPredict\r\n  //         );\r\n          \r\n  //         if (existingItem) {\r\n  //           // Preserve existing quantity/weight for previously predicted items\r\n  //           if (newItem.weight_flag === 1) {\r\n  //             return {\r\n  //               ...newItem,\r\n  //               weight: existingItem.weight\r\n  //             };\r\n  //           } else {\r\n  //             return {\r\n  //               ...newItem,\r\n  //               quantity: existingItem.quantity\r\n  //             };\r\n  //           }\r\n  //         }\r\n  //         return newItem;\r\n  //       });\r\n\r\n  //       // Combine manual and predicted items\r\n  //       const updatedCartItems = [...manualItems, ...predictedItems];\r\n  //       localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  //       return updatedCartItems;\r\n  //     });\r\n  \r\n  //     setWeight(weight);\r\n  //   } catch (error) {\r\n  //     console.error(\"Error capturing or sending image:\", error);\r\n  //   }\r\n  // }, []);\r\n\r\n\r\n  const captureAndSendImage = useCallback(async () => {\r\n    try {\r\n      toast.info(\"Capturing image...\", { autoClose: 800 });\r\n  \r\n      const response = await fetch(\"http://*************:9000/image\");\r\n      if (!response.ok) {\r\n        throw new Error(`Image capture failed: ${response.statusText}`);\r\n      }\r\n      const blob = await response.blob();\r\n  \r\n      const formData = new FormData();\r\n      formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n  \r\n      toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n  \r\n      const predictResponse = await fetch(\"http://*************:9000/predict\", {\r\n        method: \"POST\",\r\n        body: formData,\r\n      });\r\n  \r\n      if (!predictResponse.ok) {\r\n        throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n      }\r\n  \r\n      const data = await predictResponse.json();\r\n      console.log(data);\r\n      const { yolo_tag, clip_tag, weight_flag } = data;\r\n  \r\n      const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n      if (!weightResponse.ok) {\r\n        throw new Error(\"Failed to fetch weight\");\r\n      }\r\n      const weightData = await weightResponse.json();\r\n      const weight = weightData.weight;\r\n  \r\n      setClipTag(clip_tag);\r\n      console.log(\"Clip Tag: \", clip_tag);\r\n                           \r\n      // Process predicted items\r\n      const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n      \r\n      // Calculate product counts here, after tags is defined\r\n      const productCounts = tags.reduce((acc, product) => {\r\n        acc[product] = (acc[product] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      const newCartItems = await Promise.all(\r\n        tags.map(async (productName, index) => {\r\n          const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n          const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n          const priceResponse = await fetch(\r\n            `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n          );\r\n  \r\n          if (!priceResponse.ok) {\r\n            throw new Error(`Failed to fetch price for ${productName}`);\r\n          }\r\n  \r\n          const priceData = await priceResponse.json();\r\n          const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n  \r\n          const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n  \r\n          return {\r\n            productName,\r\n            weight_flag: isWeightBased ? 1 : 0,\r\n            alternatives: [productName, ...alternatives],\r\n            ...(isWeightBased \r\n              ? { weight: (weight || 0) * productCounts[productName], price: price || 0 } \r\n              : { quantity: productCounts[productName], price: price || 0 }\r\n            ),\r\n            fromPredict: true\r\n          };\r\n        })\r\n      );\r\n  \r\n      // Update cart items\r\n      setCartItems((prevCartItems) => {\r\n        // Keep manually added items\r\n        const manualItems = prevCartItems.filter(item => !item.fromPredict);\r\n        \r\n        // Get unique predicted items to avoid duplicates\r\n        const uniquePredictedItems = Array.from(new Set(newCartItems.map(item => item.productName)))\r\n          .map(productName => {\r\n            const item = newCartItems.find(item => item.productName === productName);\r\n            const existingItem = prevCartItems.find(\r\n              prevItem => prevItem.productName === productName && prevItem.fromPredict\r\n            );\r\n            \r\n            if (existingItem) {\r\n              // Preserve existing quantity/weight for previously predicted items\r\n              if (item.weight_flag === 1) {\r\n                return {\r\n                  ...item,\r\n                  weight: existingItem.weight\r\n                };\r\n              } else {\r\n                return {\r\n                  ...item,\r\n                  quantity: existingItem.quantity\r\n                };\r\n              }\r\n            }\r\n            return item;\r\n          });\r\n\r\n        // Combine manual and predicted items\r\n        const updatedCartItems = [...manualItems, ...uniquePredictedItems];\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n  \r\n      setWeight(weight);\r\n    } catch (error) {\r\n      console.error(\"Error capturing or sending image:\", error);\r\n    }\r\n  }, []);\r\n\r\n// Modified addProduct function\r\nconst addProduct = (product) => {\r\n  setCartItems((prevCartItems) => {\r\n    const updatedCartItems = [...prevCartItems];\r\n    const existingItemIndex = updatedCartItems.findIndex(\r\n      (item) => item.productName === product.productName && !item.fromPredict\r\n    );\r\n\r\n    if (existingItemIndex === -1) {\r\n      // Add new item without fromPredict flag\r\n      updatedCartItems.push({\r\n        ...product,\r\n        fromPredict: false\r\n      });\r\n    } else {\r\n      // Update existing manual item\r\n      const existingItem = updatedCartItems[existingItemIndex];\r\n      if (product.weight_flag === 1) {\r\n        updatedCartItems[existingItemIndex] = {\r\n          ...existingItem,\r\n          weight: (existingItem.weight || 0) + (product.weight || 0)\r\n        };\r\n      } else {\r\n        updatedCartItems[existingItemIndex] = {\r\n          ...existingItem,\r\n          quantity: (existingItem.quantity || 1) + (product.quantity || 1)\r\n        };\r\n      }\r\n    }\r\n\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n    return updatedCartItems;\r\n  });\r\n};\r\n  const handleIncreaseQuantity = (productName) => {\r\n    const cartItems = JSON.parse(localStorage.getItem(\"cartItems\")) || [];\r\n    const updatedCartItems = cartItems.map((item) =>\r\n      item.productName === productName\r\n        ? { ...item, quantity: (item.quantity || 1) + 1 }\r\n        : item\r\n    );\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n    setCartItems(updatedCartItems);\r\n  };\r\n\r\n  const handleDecreaseQuantity = (productName) => {\r\n    setCartItems((prevItems) => {\r\n      const updatedCartItems = prevItems\r\n        .map((item) => {\r\n          if (item.productName === productName) {\r\n            const newQuantity = (item.quantity || 1) - 1;\r\n            return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;\r\n          }\r\n          return item;\r\n        })\r\n        .filter(Boolean);\r\n\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n      return updatedCartItems;\r\n    });\r\n  };\r\n\r\n  const handleRemoveTag = (tagToRemove) => {\r\n    setCartItems((prevCartItems) => {\r\n      const indexToRemove = prevCartItems.findIndex(\r\n        (item) => item.productName === tagToRemove\r\n      );\r\n      if (indexToRemove === -1) return prevCartItems;\r\n      const updatedCartItems = [\r\n        ...prevCartItems.slice(0, indexToRemove),\r\n        ...prevCartItems.slice(indexToRemove + 1),\r\n      ];\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n\r\n      return updatedCartItems;\r\n    });\r\n  };\r\n\r\n  const handleAddToCart = async (item) => {\r\n    const isWeightBased = WEIGHT_BASED_ITEMS.has(item.productName);\r\n    let currentWeight = 0;\r\n\r\n    if (isWeightBased) {\r\n      try {\r\n        const response = await fetch(\"http://192.168.1.50:9000/weight\");\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          currentWeight = data.weight || 0;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching weight:\", error);\r\n        toast.error(\"Failed to get weight from scale\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    let updatedCartItems;\r\n\r\n    if (isWeightBased) {\r\n      updatedCartItems = [\r\n        ...cartItems,\r\n        {\r\n          ...item,\r\n          weight_flag: 1,\r\n          alternatives: [item.productName],\r\n          weight: currentWeight,\r\n          price: item.pricePerKg || item.price || 0,\r\n        },\r\n      ];\r\n      toast.info(`Added ${currentWeight}KG of ${item.productName} to cart`);\r\n    } else {\r\n      const existingItemIndex = cartItems.findIndex(\r\n        (cartItem) =>\r\n          cartItem.productName === item.productName && !cartItem.weight_flag\r\n      );\r\n\r\n      if (existingItemIndex > -1) {\r\n        updatedCartItems = cartItems.map((cartItem, index) => {\r\n          if (index === existingItemIndex) {\r\n            const newQuantity = (cartItem.quantity || 1) + 1;\r\n            toast.info(\r\n              `Updated ${item.productName} quantity to ${newQuantity}`\r\n            );\r\n            return {\r\n              ...cartItem,\r\n              quantity: newQuantity,\r\n              price: item.price || 0,\r\n            };\r\n          }\r\n          return cartItem;\r\n        });\r\n      } else {\r\n        updatedCartItems = [\r\n          ...cartItems,\r\n          {\r\n            ...item,\r\n            weight_flag: 0,\r\n            alternatives: [item.productName],\r\n            quantity: 1,\r\n            price: item.price || 0,\r\n          },\r\n        ];\r\n        toast.info(`Added ${item.productName} to cart`);\r\n      }\r\n    }\r\n\r\n    setCartItems(updatedCartItems);\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  };\r\n\r\n  const handleOpenAddProductDialog = () => {\r\n    setOpenAddProductDialog(true);\r\n  };\r\n\r\n  const handleCloseAddProductDialog = () => {\r\n    setOpenAddProductDialog(false);\r\n    setNewProductName(\"\");\r\n    setNewProductPrice(\"\");\r\n    setNewProductQuantity(1);\r\n    setNewProductShortcode(\"\");\r\n  };\r\n\r\n  const handleAddProductSubmit = () => {\r\n    if (!newProductName || !newProductPrice) {\r\n      setError(\"Please enter a product name and price.\");\r\n      return;\r\n    }\r\n\r\n    const newProduct = {\r\n      _id: Date.now().toString(),\r\n      productName: newProductName,\r\n      price: parseFloat(newProductPrice),\r\n      quantity: newProductQuantity,\r\n      shortcode: newProductShortcode,\r\n    };\r\n\r\n    setItems([...items, newProduct]);\r\n    handleAddToCart(newProduct);\r\n    handleCloseAddProductDialog();\r\n  };\r\n\r\n  const handleClick = () => {\r\n    captureAndSendImage();\r\n    setTimeout(() => setClicked(false), 600);\r\n  };\r\n\r\n  // Demo function to add sample items for testing\r\n  const addDemoItems = () => {\r\n    const demoItems = [\r\n      {\r\n        productName: \"Apple\",\r\n        price: 150,\r\n        quantity: 2,\r\n        weight_flag: 0,\r\n        alternatives: [\"Apple\", \"Green Apple\", \"Red Apple\"],\r\n        fromPredict: false\r\n      },\r\n      {\r\n        productName: \"Banana\",\r\n        price: 80,\r\n        quantity: 3,\r\n        weight_flag: 0,\r\n        alternatives: [\"Banana\", \"Ripe Banana\"],\r\n        fromPredict: false\r\n      },\r\n      {\r\n        productName: \"Rice\",\r\n        price: 60,\r\n        weight: 1.5,\r\n        weight_flag: 1,\r\n        alternatives: [\"Rice\", \"Basmati Rice\", \"Brown Rice\"],\r\n        fromPredict: false\r\n      }\r\n    ];\r\n\r\n    setCartItems(demoItems);\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(demoItems));\r\n    toast.success(\"Demo items added to cart!\");\r\n  };\r\n\r\n  const handleClearCart = () => {\r\n    setCartItems([]); // Clear cart items state\r\n    localStorage.removeItem(\"cartItems\"); // Remove from localStorage\r\n    toast.success(\"Cart cleared successfully!\"); // Optional success message\r\n  };\r\n\r\n  const fetchSearchResults = async (query) => {\r\n    try {\r\n      const response = await fetch(\r\n        `http://localhost:5000/search?query=${encodeURIComponent(query)}`\r\n      );\r\n      if (!response.ok) throw new Error(\"Failed to fetch search results\");\r\n\r\n      const data = await response.json();\r\n      const formattedResults = data.map((item) => ({\r\n        name: item.productName, // Ensure this matches your API field\r\n        price: item.price, // Ensure this matches your API field\r\n      }));\r\n\r\n      setDisplayedItems(formattedResults);\r\n    } catch (error) {\r\n      console.error(\"Error fetching search results:\", error);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (query) => {\r\n    setSearchQuery(query);\r\n    if (query.trim() === \"\") {\r\n      setDisplayedItems([]);\r\n      return;\r\n    }\r\n    fetchSearchResults(query);\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const query = e.target.value;\r\n    setSearchQuery(query);\r\n    handleSearch(query);\r\n  };\r\n\r\n  const handleWeightChange = (productName, newWeight) => {\r\n    setCartItems((prevItems) => {\r\n      const updatedItems = prevItems.map((item) => {\r\n        if (item.productName === productName) {\r\n          const weight = Math.max(\r\n            0,\r\n            Math.round(parseFloat(newWeight || 0) * 1000) / 1000\r\n          );\r\n          return {\r\n            ...item,\r\n            weight: weight,\r\n          };\r\n        }\r\n        return item;\r\n      });\r\n\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedItems));\r\n      return updatedItems;\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Header />\r\n      <div className=\"w-full max-w-full mx-auto px-4 py-6\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-140px)]\">\r\n          {/* Left Column - Cart Items */}\r\n          <div className=\"bg-white rounded-xl shadow-lg p-6 overflow-hidden flex flex-col\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-gray-800 flex items-center\">\r\n                <FontAwesomeIcon icon={faShoppingCart} className=\"mr-3 text-blue-600\" />\r\n                Your Cart ({cartItems.length} items)\r\n              </h2>\r\n              <div className=\"flex gap-2\">\r\n                <Button\r\n                  onClick={() => setOpenSearchDialog(true)}\r\n                  variant=\"outlined\"\r\n                  size=\"small\"\r\n                  startIcon={<SearchOutlinedIcon />}\r\n                >\r\n                  Search\r\n                </Button>\r\n                <Button\r\n                  onClick={() => setOpenAddProductDialog(true)}\r\n                  variant=\"outlined\"\r\n                  size=\"small\"\r\n                  startIcon={<AddOutlinedIcon />}\r\n                >\r\n                  Add\r\n                </Button>\r\n                <Button\r\n                  onClick={handleClearCart}\r\n                  variant=\"outlined\"\r\n                  color=\"error\"\r\n                  size=\"small\"\r\n                >\r\n                  Clear\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            {/* Empty Cart State */}\r\n            {cartItems.length === 0 && (\r\n              <div className=\"flex-1 flex flex-col items-center justify-center text-gray-500\">\r\n                <FontAwesomeIcon icon={faShoppingCart} className=\"text-6xl mb-4 text-gray-300\" />\r\n                <h3 className=\"text-xl font-semibold mb-2\">Your cart is empty</h3>\r\n                <p className=\"text-center\">Add some items to get started!</p>\r\n                <Button\r\n                  onClick={() => setOpenSearchDialog(true)}\r\n                  variant=\"contained\"\r\n                  className=\"mt-4\"\r\n                  startIcon={<SearchOutlinedIcon />}\r\n                >\r\n                  Browse Products\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {/* Cart Items List */}\r\n            {cartItems.length > 0 && (\r\n              <div className=\"flex-1 overflow-hidden\">\r\n                <div className=\"h-full overflow-y-auto pr-2 space-y-3 custom-scrollbar\">\r\n                  {cartItems.map((item, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow\"\r\n                    >\r\n                      <div className=\"flex items-start justify-between mb-3\">\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <FormControl size=\"small\" className=\"w-full max-w-xs\">\r\n                            <Select\r\n                              value={item.selectedProduct || item.productName}\r\n                              onChange={(e) =>\r\n                                handleDropdownChange(index, e.target.value)\r\n                              }\r\n                              displayEmpty\r\n                              className=\"text-sm\"\r\n                            >\r\n                              {item.alternatives &&\r\n                                item.alternatives.map((alternative, i) => (\r\n                                  <MenuItem key={i} value={alternative}>\r\n                                    {alternative}\r\n                                  </MenuItem>\r\n                                ))}\r\n                            </Select>\r\n                          </FormControl>\r\n                        </div>\r\n                        <IconButton\r\n                          onClick={() => handleRemoveTag(item.productName)}\r\n                          size=\"small\"\r\n                          className=\"text-red-500 hover:bg-red-50\"\r\n                        >\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-4\">\r\n                          {item.weight_flag === 1 ? (\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-sm text-gray-600\">Weight:</span>\r\n                              <TextField\r\n                                type=\"number\"\r\n                                value={parseFloat(item.weight || 0)}\r\n                                onChange={(e) =>\r\n                                  handleWeightChange(\r\n                                    item.productName,\r\n                                    e.target.value\r\n                                  )\r\n                                }\r\n                                size=\"small\"\r\n                                className=\"w-20\"\r\n                                slotProps={{\r\n                                  input: {\r\n                                    endAdornment: (\r\n                                      <span className=\"text-xs text-gray-500\">KG</span>\r\n                                    ),\r\n                                    inputProps: { min: 0, step: 0.001 },\r\n                                  }\r\n                                }}\r\n                              />\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-sm text-gray-600\">Qty:</span>\r\n                              <div className=\"flex items-center space-x-1 bg-white rounded-lg border\">\r\n                                <IconButton\r\n                                  onClick={() =>\r\n                                    handleDecreaseQuantity(item.productName)\r\n                                  }\r\n                                  size=\"small\"\r\n                                  className=\"text-red-500 hover:bg-red-50\"\r\n                                >\r\n                                  <RemoveIcon fontSize=\"small\" />\r\n                                </IconButton>\r\n                                <span className=\"px-3 py-1 text-sm font-medium\">\r\n                                  {item.quantity || 1}\r\n                                </span>\r\n                                <IconButton\r\n                                  onClick={() =>\r\n                                    handleIncreaseQuantity(item.productName)\r\n                                  }\r\n                                  size=\"small\"\r\n                                  className=\"text-green-500 hover:bg-green-50\"\r\n                                >\r\n                                  <AddIcon fontSize=\"small\" />\r\n                                </IconButton>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"text-right\">\r\n                          <div className=\"text-sm text-gray-600\">\r\n                            ₹{item.price} {item.weight_flag === 1 ? '/kg' : '/item'}\r\n                          </div>\r\n                          <div className=\"text-lg font-semibold text-gray-900\">\r\n                            ₹{(\r\n                              item.price *\r\n                              (item.weight_flag === 1\r\n                                ? item.weight\r\n                                : item.quantity)\r\n                            ).toFixed(2)}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Right Column - Summary & Actions */}\r\n          <div className=\"bg-white rounded-xl shadow-lg p-6 flex flex-col\">\r\n            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">Order Summary</h3>\r\n\r\n            {/* Quick Actions */}\r\n            <div className=\"mb-6\">\r\n              <h4 className=\"text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide\">Quick Actions</h4>\r\n              <div className=\"grid grid-cols-1 gap-3\">\r\n                <Button\r\n                  onClick={handleClick}\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  size=\"large\"\r\n                  startIcon={<FontAwesomeIcon icon={faShoppingCart} />}\r\n                  className=\"w-full\"\r\n                >\r\n                  Scan Items\r\n                </Button>\r\n                <Button\r\n                  onClick={() => setOpenSearchDialog(true)}\r\n                  variant=\"outlined\"\r\n                  size=\"large\"\r\n                  startIcon={<SearchOutlinedIcon />}\r\n                  className=\"w-full\"\r\n                >\r\n                  Search Products\r\n                </Button>\r\n                <Button\r\n                  onClick={() => setOpenAddProductDialog(true)}\r\n                  variant=\"outlined\"\r\n                  size=\"large\"\r\n                  startIcon={<AddOutlinedIcon />}\r\n                  className=\"w-full\"\r\n                >\r\n                  Add Custom Item\r\n                </Button>\r\n                <Button\r\n                  onClick={addDemoItems}\r\n                  variant=\"outlined\"\r\n                  size=\"large\"\r\n                  className=\"w-full\"\r\n                  color=\"secondary\"\r\n                >\r\n                  Add Demo Items\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Order Details */}\r\n            <div className=\"flex-1\">\r\n              <h4 className=\"text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide\">Order Details</h4>\r\n              <div className=\"space-y-3 mb-6\">\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Items in cart:</span>\r\n                  <span className=\"font-medium\">{cartItems.length}</span>\r\n                </div>\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Subtotal:</span>\r\n                  <span className=\"font-medium\">₹{subtotal}</span>\r\n                </div>\r\n                <div className=\"border-t pt-3\">\r\n                  <div className=\"flex justify-between text-lg font-bold\">\r\n                    <span>Total:</span>\r\n                    <span className=\"text-blue-600\">\r\n                      {Number(totalAmount).toLocaleString(\"en-IN\", {\r\n                        style: \"currency\",\r\n                        currency: \"INR\",\r\n                        minimumFractionDigits: 0,\r\n                        maximumFractionDigits: 3,\r\n                      })}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"success\"\r\n                size=\"large\"\r\n                className=\"w-full\"\r\n                disabled={cartItems.length === 0}\r\n              >\r\n                Proceed to Checkout\r\n              </Button>\r\n              <Button\r\n                onClick={handleClearCart}\r\n                variant=\"outlined\"\r\n                color=\"error\"\r\n                size=\"large\"\r\n                className=\"w-full\"\r\n                disabled={cartItems.length === 0}\r\n              >\r\n                Clear Cart\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Powered by */}\r\n            <div className=\"mt-6 pt-4 border-t text-center\">\r\n              <p className=\"text-xs text-gray-500\">Powered by Synecx AI</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search Dialog */}\r\n        <Dialog\r\n          open={openSearchDialog}\r\n          onClose={() => setOpenSearchDialog(false)}\r\n          fullWidth\r\n          maxWidth=\"sm\"\r\n        >\r\n          <DialogTitle>\r\n            Search Products\r\n            <IconButton\r\n              edge=\"end\"\r\n              color=\"inherit\"\r\n              onClick={() => setOpenSearchDialog(false)}\r\n              aria-label=\"close\"\r\n              sx={{ position: \"absolute\", right: 10, top: 10 }}\r\n            >\r\n              <DeleteIcon />\r\n            </IconButton>\r\n          </DialogTitle>\r\n          <DialogContent>\r\n            <TextField\r\n              fullWidth\r\n              variant=\"outlined\"\r\n              placeholder=\"Search for a product...\"\r\n              value={searchQuery}\r\n              onChange={handleInputChange}\r\n              sx={{ mb: 2 }}\r\n            />\r\n            {displayedItems.length > 0 ? (\r\n              <List>\r\n                {displayedItems.map((item, index) => (\r\n                  <ListItem\r\n                    key={index}\r\n                    button\r\n                    onClick={() => {\r\n                      handleAddToCart(item);\r\n                      setOpenSearchDialog(false);\r\n                    }}\r\n                    sx={{\r\n                      display: \"flex\",\r\n                      justifyContent: \"space-between\",\r\n                      borderBottom: \"1px solid #ddd\",\r\n                      padding: \"8px\",\r\n                    }}\r\n                  >\r\n                    <Typography>{item.name}</Typography>\r\n                    <Typography\r\n                      sx={{\r\n                        fontWeight: \"semibold\",\r\n                        color: \"#000000\",\r\n                        mt: 2,\r\n                      }}\r\n                    >\r\n                      ₹{item.price}\r\n                    </Typography>\r\n                  </ListItem>\r\n                ))}\r\n              </List>\r\n            ) : (\r\n              <Typography\r\n                sx={{ textAlign: \"center\", mt: 2, color: \"gray\" }}\r\n              >\r\n                No products found.\r\n              </Typography>\r\n            )}\r\n          </DialogContent>\r\n        </Dialog>\r\n        {/* Add Product Dialog */}\r\n        <Dialog\r\n          open={openAddProductDialog}\r\n          onClose={handleCloseAddProductDialog}\r\n        >\r\n          <DialogTitle>Add a Product</DialogTitle>\r\n          <DialogContent>\r\n            <TextField\r\n              id=\"productName\"\r\n              variant=\"outlined\"\r\n              label=\"Product Name\"\r\n              value={newProductName}\r\n              onChange={(e) => setNewProductName(e.target.value)}\r\n              sx={{ mt: 2, width: \"100%\" }}\r\n              required\r\n            />\r\n            <TextField\r\n              id=\"productPrice\"\r\n              variant=\"outlined\"\r\n              label=\"Product Price\"\r\n              value={newProductPrice}\r\n              onChange={(e) => setNewProductPrice(e.target.value)}\r\n              sx={{ mt: 2, width: \"100%\" }}\r\n              required\r\n            />\r\n          </DialogContent>\r\n          <DialogActions>\r\n            <Button onClick={handleCloseAddProductDialog}>Cancel</Button>\r\n            <Button\r\n              onClick={handleAddProductSubmit}\r\n              color=\"primary\"\r\n              variant=\"contained\"\r\n            >\r\n              Add Product\r\n            </Button>\r\n          </DialogActions>\r\n        </Dialog>\r\n\r\n        {/* Hidden canvas for motion detection */}\r\n        <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n\r\n        {/* Toast notifications */}\r\n        {showMotionToast && (\r\n          <div className=\"fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n            Motion Detected!\r\n          </div>\r\n        )}\r\n        {showItemToast && (\r\n          <div className=\"fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n            Item placed\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Cart;\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,8DAA8D;AAC9D,kEAAkE;AAClE,iCAAiC;AAEjC,kBAAkB;AAClB,eAAe;AACf,eAAe;AACf,mBAAmB;AACnB,oBAAoB;AACpB,IAAI;AACJ,wBAAwB;AACxB,iDAAiD;AACjD,IAAI;AAEJ,kEAAkE;AAClE,8DAA8D;AAE9D,kCAAkC;AAClC,cAAc;AACd,cAAc;AACd,wBAAwB;AACxB,yBAAyB;AACzB,QAAQ;AAER,mFAAmF;AACnF,SAAS;AACT,OAAO;AACP,sBAAsB;AACtB,gFAAgF;AAChF,mFAAmF;AACnF,yCAAyC;AAEzC,wDAAwD;AACxD,mFAAmF;AACnF,qEAAqE;AACrE,YAAY;AACZ,sCAAsC;AACtC,kFAAkF;AAClF,wBAAwB;AACxB,cAAc;AACd,WAAW;AAEX,2BAA2B;AAC3B,8CAA8C;AAC9C,qEAAqE;AACrE,iBAAiB;AACjB,qEAAqE;AACrE,oBAAoB;AACpB,UAAU;AACV,wBAAwB;AACxB,+DAA+D;AAC/D,kBAAkB;AAClB,QAAQ;AACR,OAAO;AAEP,8CAA8C;AAC9C,gDAAgD;AAChD,YAAY;AACZ,wCAAwC;AAExC,0CAA0C;AAC1C,iEAAiE;AACjE,8CAA8C;AAC9C,WAAW;AACX,2BAA2B;AAE3B,yCAAyC;AACzC,uDAAuD;AAEvD,0EAA0E;AAC1E,0BAA0B;AAC1B,0BAA0B;AAC1B,YAAY;AAEZ,2BAA2B;AAC3B,8CAA8C;AAC9C,sDAAsD;AAEtD,kDAAkD;AAElD,uDAAuD;AACvD,sDAAsD;AACtD,0DAA0D;AAC1D,+EAA+E;AAC/E,mDAAmD;AACnD,eAAe;AACf,aAAa;AAEb,0CAA0C;AAC1C,iBAAiB;AACjB,qEAAqE;AACrE,UAAU;AACV,wBAAwB;AACxB,+DAA+D;AAC/D,QAAQ;AACR,OAAO;AAEP,8BAA8B;AAC9B,sBAAsB;AACtB,2BAA2B;AAC3B,mCAAmC;AACnC,sCAAsC;AACtC,4BAA4B;AAC5B,6DAA6D;AAE7D,iDAAiD;AACjD,cAAc;AACd,0EAA0E;AAC1E,6CAA6C;AAC7C,8CAA8C;AAC9C,iDAAiD;AACjD,qCAAqC;AACrC,8CAA8C;AAC9C,2BAA2B;AAC3B,kCAAkC;AAClC,iCAAiC;AACjC,iCAAiC;AACjC,qCAAqC;AACrC,gDAAgD;AAChD,mDAAmD;AACnD,gCAAgC;AAEhC,0CAA0C;AAC1C,4CAA4C;AAE5C,gEAAgE;AAChE,0CAA0C;AAE1C,8BAA8B;AAC9B,+DAA+D;AAC/D,0CAA0C;AAC1C,wBAAwB;AACxB,gBAAgB;AAEhB,+DAA+D;AAC/D,yDAAyD;AACzD,sBAAsB;AACtB,sBAAsB;AACtB,uBAAuB;AACvB,uBAAuB;AACvB,iBAAiB;AACjB,qEAAqE;AAErE,sCAAsC;AACtC,kDAAkD;AAClD,oCAAoC;AACpC,mCAAmC;AACnC,yBAAyB;AACzB,0BAA0B;AAC1B,4BAA4B;AAC5B,gCAAgC;AAChC,mBAAmB;AACnB,kEAAkE;AAClE,8CAA8C;AAC9C,mFAAmF;AACnF,kBAAkB;AAClB,gBAAgB;AAEhB,kDAAkD;AAClD,wCAAwC;AACxC,cAAc;AACd,aAAa;AACb,0BAA0B;AAC1B,uEAAuE;AACvE,UAAU;AACV,SAAS;AAET,+DAA+D;AAE/D,4CAA4C;AAC5C,eAAe;AAEf,uEAAuE;AACvE,iDAAiD;AACjD,8DAA8D;AAC9D,iDAAiD;AACjD,kFAAkF;AAClF,gCAAgC;AAChC,QAAQ;AACR,uBAAuB;AACvB,OAAO;AAEP,2BAA2B;AAC3B,mCAAmC;AACnC,sCAAsC;AACtC,qBAAqB;AACrB,sBAAsB;AACtB,wBAAwB;AACxB,wBAAwB;AACxB,uCAAuC;AACvC,0DAA0D;AAC1D,+DAA+D;AAC/D,gEAAgE;AAChE,4BAA4B;AAC5B,mDAAmD;AACnD,qDAAqD;AACrD,2CAA2C;AAC3C,6CAA6C;AAC7C,4EAA4E;AAC5E,gBAAgB;AAChB,cAAc;AACd,YAAY;AACZ,sCAAsC;AACtC,wCAAwC;AACxC,YAAY;AACZ,UAAU;AACV,QAAQ;AACR,0BAA0B;AAC1B,OAAO;AAEP,kCAAkC;AAClC,kDAAkD;AAClD,mBAAmB;AAEnB,aAAa;AACb,sFAAsF;AACtF,mBAAmB;AAEnB,aAAa;AACb,gBAAgB;AAChB,2BAA2B;AAC3B,+BAA+B;AAC/B,sCAAsC;AACtC,kCAAkC;AAClC,2BAA2B;AAC3B,0BAA0B;AAC1B,kBAAkB;AAClB,6BAA6B;AAC7B,qCAAqC;AACrC,aAAa;AACb,UAAU;AACV,eAAe;AACf,2BAA2B;AAC3B,2CAA2C;AAC3C,gCAAgC;AAChC,mBAAmB;AACnB,YAAY;AACZ,wBAAwB;AACxB,2BAA2B;AAC3B,6BAA6B;AAC7B,mDAAmD;AACnD,cAAc;AACd,wBAAwB;AACxB,0BAA0B;AAC1B,iBAAiB;AAEjB,yDAAyD;AACzD,8CAA8C;AAC9C,mBAAmB;AACnB,4BAA4B;AAC5B,wGAAwG;AACxG,gBAAgB;AAChB,iEAAiE;AACjE,8BAA8B;AAC9B,8BAA8B;AAC9B,qFAAqF;AAErF,8BAA8B;AAC9B,mCAAmC;AACnC,oCAAoC;AACpC,wCAAwC;AACxC,2CAA2C;AAC3C,oBAAoB;AACpB,6CAA6C;AAC7C,uEAAuE;AACvE,yBAAyB;AACzB,kCAAkC;AAClC,gCAAgC;AAChC,8BAA8B;AAC9B,qBAAqB;AACrB,gBAAgB;AAChB,kBAAkB;AAElB,qCAAqC;AACrC,qEAAqE;AACrE,wDAAwD;AACxD,qBAAqB;AACrB,0BAA0B;AAC1B,wDAAwD;AACxD,6BAA6B;AAC7B,0BAA0B;AAC1B,iBAAiB;AACjB,eAAe;AACf,+DAA+D;AAC/D,yEAAyE;AACzE,aAAa;AACb,OAAO;AACP,KAAK;AAEL,uBAAuB;AAEvB,gBAAgB;AAChB,8DAA8D;AAC9D,kEAAkE;AAClE,iCAAiC;AAEjC,kBAAkB;AAClB,eAAe;AACf,eAAe;AACf,mBAAmB;AACnB,oBAAoB;AACpB,IAAI;AACJ,wBAAwB;AACxB,iDAAiD;AACjD,IAAI;AAEJ,kEAAkE;AAClE,8DAA8D;AAE9D,kCAAkC;AAClC,cAAc;AACd,cAAc;AACd,wBAAwB;AACxB,yBAAyB;AACzB,QAAQ;AAER,mFAAmF;AACnF,SAAS;AACT,OAAO;AACL,oBAAoB;AACpB,8EAA8E;AAC9E,iFAAiF;AACjF,uCAAuC;AAEzC,wDAAwD;AACxD,mFAAmF;AACnF,qEAAqE;AACrE,YAAY;AACZ,sCAAsC;AACtC,kFAAkF;AAClF,wBAAwB;AACxB,cAAc;AACd,WAAW;AAEX,2BAA2B;AAC3B,8CAA8C;AAC9C,qEAAqE;AACrE,iBAAiB;AACjB,qEAAqE;AACrE,oBAAoB;AACpB,UAAU;AACV,wBAAwB;AACxB,+DAA+D;AAC/D,kBAAkB;AAClB,QAAQ;AACR,OAAO;AAEP,8CAA8C;AAC9C,gDAAgD;AAChD,YAAY;AACZ,wCAAwC;AAExC,0CAA0C;AAC1C,iEAAiE;AACjE,8CAA8C;AAC9C,WAAW;AACX,2BAA2B;AAE3B,yCAAyC;AACzC,uDAAuD;AAEvD,0EAA0E;AAC1E,0BAA0B;AAC1B,0BAA0B;AAC1B,YAAY;AAEZ,2BAA2B;AAC3B,8CAA8C;AAC9C,sDAAsD;AAEtD,2DAA2D;AAE3D,uDAAuD;AACvD,sDAAsD;AACtD,0DAA0D;AAC1D,+EAA+E;AAC/E,mDAAmD;AACnD,eAAe;AACf,aAAa;AAEb,0CAA0C;AAC1C,iBAAiB;AACjB,qEAAqE;AACrE,UAAU;AACV,wBAAwB;AACxB,+DAA+D;AAC/D,QAAQ;AACR,OAAO;AAEP,8BAA8B;AAC9B,sBAAsB;AACtB,2BAA2B;AAC3B,mCAAmC;AACnC,sCAAsC;AACtC,4BAA4B;AAC5B,6DAA6D;AAE7D,iDAAiD;AACjD,cAAc;AACd,0EAA0E;AAC1E,6CAA6C;AAC7C,8CAA8C;AAC9C,iDAAiD;AACjD,qCAAqC;AACrC,8CAA8C;AAC9C,2BAA2B;AAC3B,kCAAkC;AAClC,iCAAiC;AACjC,iCAAiC;AACjC,qCAAqC;AACrC,gDAAgD;AAChD,mDAAmD;AACnD,gCAAgC;AAEhC,0CAA0C;AAC1C,4CAA4C;AAE5C,gEAAgE;AAChE,0CAA0C;AAE1C,8BAA8B;AAC9B,+DAA+D;AAC/D,0CAA0C;AAC1C,wBAAwB;AACxB,gBAAgB;AAEhB,+DAA+D;AAC/D,yDAAyD;AACzD,sBAAsB;AACtB,sBAAsB;AACtB,uBAAuB;AACvB,uBAAuB;AACvB,iBAAiB;AACjB,qEAAqE;AAErE,sCAAsC;AACtC,kDAAkD;AAClD,oCAAoC;AACpC,mCAAmC;AACnC,yBAAyB;AACzB,0BAA0B;AAC1B,4BAA4B;AAC5B,gCAAgC;AAChC,mBAAmB;AACnB,kEAAkE;AAClE,8CAA8C;AAC9C,mFAAmF;AACnF,kBAAkB;AAClB,gBAAgB;AAEhB,kDAAkD;AAClD,wCAAwC;AACxC,cAAc;AACd,aAAa;AACb,0BAA0B;AAC1B,uEAAuE;AACvE,UAAU;AACV,SAAS;AAET,+DAA+D;AAE/D,4CAA4C;AAC5C,eAAe;AAEf,uEAAuE;AACvE,iDAAiD;AACjD,8DAA8D;AAC9D,iDAAiD;AACjD,kFAAkF;AAClF,gCAAgC;AAChC,QAAQ;AACR,uBAAuB;AACvB,OAAO;AAEP,2BAA2B;AAC3B,mCAAmC;AACnC,sCAAsC;AACtC,qBAAqB;AACrB,sBAAsB;AACtB,wBAAwB;AACxB,wBAAwB;AACxB,uCAAuC;AACvC,0DAA0D;AAC1D,+DAA+D;AAC/D,gEAAgE;AAChE,4BAA4B;AAC5B,mDAAmD;AACnD,qDAAqD;AACrD,2CAA2C;AAC3C,6CAA6C;AAC7C,4EAA4E;AAC5E,gBAAgB;AAChB,cAAc;AACd,YAAY;AACZ,sCAAsC;AACtC,wCAAwC;AACxC,YAAY;AACZ,UAAU;AACV,QAAQ;AACR,0BAA0B;AAC1B,OAAO;AAEP,kCAAkC;AAClC,kDAAkD;AAClD,mBAAmB;AAEnB,aAAa;AACb,sFAAsF;AACtF,mBAAmB;AAEnB,aAAa;AACb,gBAAgB;AAChB,2BAA2B;AAC3B,+BAA+B;AAC/B,sCAAsC;AACtC,kCAAkC;AAClC,2BAA2B;AAC3B,0BAA0B;AAC1B,kBAAkB;AAClB,6BAA6B;AAC7B,qCAAqC;AACrC,aAAa;AACb,UAAU;AACV,eAAe;AACf,2BAA2B;AAC3B,2CAA2C;AAC3C,gCAAgC;AAChC,mBAAmB;AACnB,YAAY;AACZ,wBAAwB;AACxB,2BAA2B;AAC3B,6BAA6B;AAC7B,mDAAmD;AACnD,cAAc;AACd,wBAAwB;AACxB,0BAA0B;AAC1B,iBAAiB;AAEjB,yDAAyD;AACzD,8CAA8C;AAC9C,mBAAmB;AACnB,4BAA4B;AAC5B,wGAAwG;AACxG,gBAAgB;AAChB,iEAAiE;AACjE,8BAA8B;AAC9B,8BAA8B;AAC9B,qFAAqF;AAErF,8BAA8B;AAC9B,mCAAmC;AACnC,oCAAoC;AACpC,wCAAwC;AACxC,2CAA2C;AAC3C,oBAAoB;AACpB,6CAA6C;AAC7C,uEAAuE;AACvE,yBAAyB;AACzB,kCAAkC;AAClC,gCAAgC;AAChC,8BAA8B;AAC9B,qBAAqB;AACrB,gBAAgB;AAChB,kBAAkB;AAElB,qCAAqC;AACrC,qEAAqE;AACrE,wDAAwD;AACxD,qBAAqB;AACrB,0BAA0B;AAC1B,wDAAwD;AACxD,6BAA6B;AAC7B,0BAA0B;AAC1B,iBAAiB;AACjB,eAAe;AACf,+DAA+D;AAC/D,yEAAyE;AACzE,aAAa;AACb,OAAO;AACP,KAAK;AACL,uBAAuB;;;;;AAIvB;AAqBA;AACA;AAMA;AALA;AAtBA;AAyBA;AACA;AA1BA;AAAA;AAAA;AAkBA;AACA;AAKA;AADA;AAvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;AA6BA,MAAM,qBAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAM,OAAO,CAAC,EAAE,kBAAkB,EAAE;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE7C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE1C,kCAAkC;IAClC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;QAAK,OAAO;QAAK,QAAQ;IAAI;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,+BAA+B;IAEtF,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK;YACxB,IAAI,KAAK,WAAW,KAAK,GAAG;gBAC1B,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,CAAC;YAC7C;YACA,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC;QAC/C,GAAG;IACL;IAEA,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,oBAAoB;QACvC,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAW;KAAmB;IAEhC,oBAAoB;IACpB,yBAAyB;IACzB,iCAAiC;IACjC,oCAAoC;IACpC,0BAA0B;IAC1B,iCAAiC;IAEjC,+CAA+C;IAC/C,YAAY;IACZ,yEAAyE;IACzE,4CAA4C;IAC5C,+CAA+C;IAC/C,sCAAsC;IACtC,0CAA0C;IAC1C,yBAAyB;IACzB,+BAA+B;IAC/B,4CAA4C;IAC5C,+CAA+C;IAC/C,sCAAsC;IACtC,wCAAwC;IAExC,4DAA4D;IAC5D,sCAAsC;IAEtC,0BAA0B;IAC1B,2DAA2D;IAC3D,sCAAsC;IACtC,oBAAoB;IACpB,YAAY;IAEZ,2DAA2D;IAE3D,gFAAgF;IAChF,iEAAiE;IAEjE,kCAAkC;IAClC,8CAA8C;IAC9C,gCAAgC;IAChC,+BAA+B;IAC/B,qBAAqB;IACrB,sBAAsB;IACtB,wBAAwB;IACxB,4BAA4B;IAC5B,eAAe;IAEf,8DAA8D;IAC9D,6DAA6D;IAC7D,wCAAwC;IACxC,gDAAgD;IAChD,gDAAgD;IAChD,0CAA0C;IAC1C,iCAAiC;IACjC,2CAA2C;IAC3C,6CAA6C;IAC7C,uBAAuB;IACvB,oDAAoD;IACpD,iDAAiD;IACjD,uDAAuD;IACvD,wCAAwC;IACxC,mDAAmD;IACnD,kDAAkD;IAClD,+DAA+D;IAC/D,mCAAmC;IACnC,2CAA2C;IAC3C,0BAA0B;IAC1B,gBAAgB;IAChB,cAAc;IACd,YAAY;IAEZ,8CAA8C;IAC9C,oCAAoC;IACpC,WAAW;IACX,wBAAwB;IACxB,qEAAqE;IACrE,QAAQ;IACR,OAAO;IAEP,6DAA6D;IAC7D,0CAA0C;IAC1C,aAAa;IAGd,OAAO;IACN,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;QACjB,MAAM,gBAAgB;QACtB,MAAM,uBAAuB;QAC7B,IAAI,eAAe;QACnB,IAAI,mBAAmB;QAEzB,KAAK;QACL,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,6BAA6B;gBAC7B,MAAM,QAAQ,IAAI,OAAO,KAAK;gBAC9B,MAAM,GAAG,GAAG;gBACZ,MAAM,MAAM,GAAG;oBACb,MAAM,SAAS,UAAU,OAAO;oBAChC,MAAM,MAAM,OAAO,UAAU,CAAC;oBAC9B,OAAO,KAAK,GAAG,MAAM,KAAK;oBAC1B,OAAO,MAAM,GAAG,MAAM,MAAM;oBAE5B,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAC/C,IAAI,SAAS,CAAC,OAAO,GAAG;oBAExB;oBACA,IAAI,eAAe,yBAAyB,GAAG;wBAC7C,IAAI,eAAe,CAAC;wBACpB;oBACF;oBAEA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;oBAE5C,MAAM,mBAAmB,IAAI,YAAY,CAAC,MAAM,MAAM,OAAO;oBAC7D,MAAM,kBAAkB,YAAY;oBAEpC,IAAI,kBAAkB;wBACpB,MAAM,cAAc,aAClB,kBACA,iBACA,OACA,QACA,UACA;wBAGF,MAAM,oBAAoB,YAAY,MAAM,GAAG;wBAC/C,IAAI,qBAAqB,CAAC,YAAY,OAAO,EAAE;4BAC7C,mBAAmB;4BACnB,kBAAkB,OAAO,GAAG;4BAC5B,YAAY,OAAO,GAAG;4BAEtB,sCAAsC;4BACtC;4BAEA,WAAW;gCACT,mBAAmB;gCACnB,YAAY,OAAO,GAAG;4BACxB,GAAG;wBACL;oBACF;oBAEA,mBAAmB;oBACnB,IAAI,eAAe,CAAC;gBACtB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;QAEA,MAAM,WAAW,YAAY,sBAAsB;QACnD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAI;IAEN,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd;YACA,cAAc,QAAQ,oCAAoC;QAC5D;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,cAAc,CAAC;QACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QAChC,MAAM,WAAW,IAAI,kBAAkB,QAAQ;QAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxE,QAAQ,CAAC,IAAI,EAAE,GAAG;QACpB;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC,UAAU,aAAa,OAAO,QAAQ,UAAU;QACpE,MAAM,cAAc,EAAE;QACtB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,UAAU,KAAK,SAAU;YACrD,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,UAAU,KAAK,SAAU;gBACpD,IAAI,WAAW;gBACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,IAAK;oBACrC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,IAAK;wBACrC,MAAM,QAAQ,IAAI,QAAQ;wBAC1B,IAAI,QAAQ,SAAS,MAAM,EAAE;4BAC3B,YAAY,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;wBAC3D;oBACF;gBACF;gBACA,IAAI,WAAW,WAAW;oBACxB,YAAY,IAAI,CAAC;wBAAE;wBAAG;oBAAE;gBAC1B;YACF;QACF;QACA,OAAO;IACT;IAGA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IACL,MAAM,WAAW,UACd,MAAM,CAAC,CAAC,KAAK;QACZ,IAAI,KAAK,WAAW,KAAK,GAAG;YAC1B,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,CAAC;QAC7C;QACA,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC;IAC/C,GAAG,GACF,OAAO,CAAC;IAGX,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW;IAE1C,MAAM,uBAAuB,OAAO,OAAO;QACzC,IAAI;YACF,MAAM,gBAAgB,mBAAmB,GAAG,CAAC;YAC7C,MAAM,gBAAgB,gBAAgB,eAAe;YACrD,MAAM,gBAAgB,MAAM,MAC1B,CAAC,sBAAsB,EAAE,cAAc,aAAa,EAAE,mBACpD,iBACC;YAGL,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,eAAe,EAAE,EAAE,cAAc,UAAU,EAAE;YAE9E;YAEA,MAAM,YAAY,MAAM,cAAc,IAAI;YAC1C,MAAM,WAAW,gBACb,UAAU,UAAU,GACpB,UAAU,QAAQ;YAEtB,aAAa,CAAC;gBACZ,MAAM,mBAAmB,cAAc,GAAG,CAAC,CAAC,MAAM;oBAChD,IAAI,MAAM,OAAO;wBACf,MAAM,cAAc;4BAClB,GAAG,IAAI;4BACP,iBAAiB;4BACjB,aAAa;4BACb,OAAO;4BACP,aAAa,gBAAgB,IAAI;wBACnC;wBAEA,IAAI,KAAK,WAAW,KAAK,CAAC,gBAAgB,IAAI,CAAC,GAAG;4BAChD,IAAI,eAAe;gCACjB,OAAO,YAAY,QAAQ;gCAC3B,YAAY,MAAM,GAAG;4BACvB,OAAO;gCACL,OAAO,YAAY,MAAM;gCACzB,YAAY,QAAQ,GAAG;4BACzB;wBACF;wBAEA,OAAO;oBACT;oBACA,OAAO;gBACT;gBAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAGA,GAAG;IACH,wDAAwD;IACxD,UAAU;IACV,4DAA4D;IAE5D,uEAAuE;IACvE,0BAA0B;IAC1B,yEAAyE;IACzE,QAAQ;IACR,0CAA0C;IAE1C,uCAAuC;IACvC,yDAAyD;IAEzD,yEAAyE;IAEzE,kGAAkG;IAClG,wBAAwB;IACxB,wBAAwB;IACxB,UAAU;IAEV,iCAAiC;IACjC,qFAAqF;IACrF,QAAQ;IAER,iDAAiD;IACjD,yBAAyB;IACzB,wDAAwD;IAExD,8EAA8E;IAC9E,gCAAgC;IAChC,mDAAmD;IACnD,QAAQ;IACR,sDAAsD;IACtD,wCAAwC;IAExC,4BAA4B;IAC5B,2CAA2C;IAE3C,+CAA+C;IAC/C,oEAAoE;IACpE,8CAA8C;IAC9C,iDAAiD;IACjD,qEAAqE;IACrE,2EAA2E;IAC3E,6CAA6C;IAC7C,oGAAoG;IACpG,aAAa;IAEb,mCAAmC;IACnC,yEAAyE;IACzE,YAAY;IAEZ,wDAAwD;IACxD,mFAAmF;IAEnF,qFAAqF;IAErF,mBAAmB;IACnB,yBAAyB;IACzB,gDAAgD;IAChD,0DAA0D;IAC1D,kHAAkH;IAClH,aAAa;IACb,WAAW;IACX,SAAS;IAET,kDAAkD;IAClD,wCAAwC;IACxC,gEAAgE;IAChE,0CAA0C;IAC1C,WAAW;IAEX,mDAAmD;IACnD,4CAA4C;IAC5C,gEAAgE;IAChE,+DAA+D;IAC/D,aAAa;IAEb,wCAAwC;IACxC,sEAAsE;IACtE,6CAA6C;IAC7C,sDAAsD;IACtD,iCAAiC;IACjC,4EAA4E;IAC5E,iBAAiB;IACjB,qBAAqB;IACrB,sDAAsD;IACtD,iCAAiC;IACjC,kFAAkF;IAClF,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,4CAA4C;IAC5C,YAAY;IACZ,YAAY;IAEZ,6EAA6E;IAC7E,iCAAiC;IACjC,UAAU;IAEV,yBAAyB;IACzB,sBAAsB;IACtB,iEAAiE;IACjE,MAAM;IACN,UAAU;IAKV,wDAAwD;IACxD,UAAU;IACV,4DAA4D;IAE5D,uEAAuE;IACvE,0BAA0B;IAC1B,yEAAyE;IACzE,QAAQ;IACR,0CAA0C;IAE1C,uCAAuC;IACvC,yDAAyD;IAEzD,yEAAyE;IAEzE,kGAAkG;IAClG,wBAAwB;IACxB,wBAAwB;IACxB,UAAU;IAEV,iCAAiC;IACjC,qFAAqF;IACrF,QAAQ;IAER,iDAAiD;IACjD,yBAAyB;IACzB,wDAAwD;IAExD,8EAA8E;IAC9E,gCAAgC;IAChC,mDAAmD;IACnD,QAAQ;IACR,sDAAsD;IACtD,wCAAwC;IAExC,4BAA4B;IAC5B,2CAA2C;IAE3C,iCAAiC;IACjC,oEAAoE;IACpE,8CAA8C;IAC9C,iDAAiD;IACjD,qEAAqE;IACrE,2EAA2E;IAC3E,6CAA6C;IAC7C,oGAAoG;IACpG,aAAa;IAEb,mCAAmC;IACnC,yEAAyE;IACzE,YAAY;IAEZ,wDAAwD;IACxD,mFAAmF;IAEnF,qFAAqF;IAErF,mBAAmB;IACnB,yBAAyB;IACzB,gDAAgD;IAChD,0DAA0D;IAC1D,kHAAkH;IAClH,6DAA6D;IAC7D,aAAa;IACb,WAAW;IACX,SAAS;IAET,2BAA2B;IAC3B,wCAAwC;IACxC,qCAAqC;IACrC,6EAA6E;IAE7E,kEAAkE;IAClE,6DAA6D;IAC7D,mDAAmD;IACnD,iFAAiF;IACjF,aAAa;IAEb,8BAA8B;IAC9B,gFAAgF;IAChF,6CAA6C;IAC7C,uBAAuB;IACvB,4BAA4B;IAC5B,4CAA4C;IAC5C,iBAAiB;IACjB,qBAAqB;IACrB,uBAAuB;IACvB,4BAA4B;IAC5B,gDAAgD;IAChD,iBAAiB;IACjB,cAAc;IACd,YAAY;IACZ,0BAA0B;IAC1B,YAAY;IAEZ,8CAA8C;IAC9C,sEAAsE;IACtE,6EAA6E;IAC7E,iCAAiC;IACjC,UAAU;IAEV,yBAAyB;IACzB,sBAAsB;IACtB,iEAAiE;IACjE,MAAM;IACN,UAAU;IAGV,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI;YACF,mJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,sBAAsB;gBAAE,WAAW;YAAI;YAElD,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,MAAM;YAE/B,mJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,mCAAmC;gBAAE,WAAW;YAAI;YAE/D,MAAM,kBAAkB,MAAM,MAAM,qCAAqC;gBACvE,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,gBAAgB,UAAU,EAAE;YAC5E;YAEA,MAAM,OAAO,MAAM,gBAAgB,IAAI;YACvC,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;YAE5C,MAAM,iBAAiB,MAAM,MAAM;YACnC,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,aAAa,MAAM,eAAe,IAAI;YAC5C,MAAM,SAAS,WAAW,MAAM;YAEhC,WAAW;YACX,QAAQ,GAAG,CAAC,cAAc;YAE1B,0BAA0B;YAC1B,MAAM,OAAO,MAAM,OAAO,CAAC,YAAY,WAAW;gBAAC;aAAS;YAE5D,uDAAuD;YACvD,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,KAAK;gBACtC,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI;gBACrC,OAAO;YACT,GAAG,CAAC;YAEJ,MAAM,eAAe,MAAM,QAAQ,GAAG,CACpC,KAAK,GAAG,CAAC,OAAO,aAAa;gBAC3B,MAAM,gBAAgB,mBAAmB,GAAG,CAAC;gBAC7C,MAAM,gBAAgB,gBAAgB,eAAe;gBACrD,MAAM,gBAAgB,MAAM,MAC1B,CAAC,sBAAsB,EAAE,cAAc,aAAa,EAAE,mBAAmB,cAAc;gBAGzF,IAAI,CAAC,cAAc,EAAE,EAAE;oBACrB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,aAAa;gBAC5D;gBAEA,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,MAAM,QAAQ,gBAAgB,UAAU,UAAU,GAAG,UAAU,QAAQ;gBAEvE,MAAM,eAAe,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAQ,QAAQ;gBAE7D,OAAO;oBACL;oBACA,aAAa,gBAAgB,IAAI;oBACjC,cAAc;wBAAC;2BAAgB;qBAAa;oBAC5C,GAAI,gBACA;wBAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,YAAY;wBAAE,OAAO,SAAS;oBAAE,IACxE;wBAAE,UAAU,aAAa,CAAC,YAAY;wBAAE,OAAO,SAAS;oBAAE,CAAC;oBAE/D,aAAa;gBACf;YACF;YAGF,oBAAoB;YACpB,aAAa,CAAC;gBACZ,4BAA4B;gBAC5B,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,WAAW;gBAElE,iDAAiD;gBACjD,MAAM,uBAAuB,MAAM,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW,IACtF,GAAG,CAAC,CAAA;oBACH,MAAM,OAAO,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;oBAC5D,MAAM,eAAe,cAAc,IAAI,CACrC,CAAA,WAAY,SAAS,WAAW,KAAK,eAAe,SAAS,WAAW;oBAG1E,IAAI,cAAc;wBAChB,mEAAmE;wBACnE,IAAI,KAAK,WAAW,KAAK,GAAG;4BAC1B,OAAO;gCACL,GAAG,IAAI;gCACP,QAAQ,aAAa,MAAM;4BAC7B;wBACF,OAAO;4BACL,OAAO;gCACL,GAAG,IAAI;gCACP,UAAU,aAAa,QAAQ;4BACjC;wBACF;oBACF;oBACA,OAAO;gBACT;gBAEF,qCAAqC;gBACrC,MAAM,mBAAmB;uBAAI;uBAAgB;iBAAqB;gBAClE,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF,GAAG,EAAE;IAEP,+BAA+B;IAC/B,MAAM,aAAa,CAAC;QAClB,aAAa,CAAC;YACZ,MAAM,mBAAmB;mBAAI;aAAc;YAC3C,MAAM,oBAAoB,iBAAiB,SAAS,CAClD,CAAC,OAAS,KAAK,WAAW,KAAK,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW;YAGzE,IAAI,sBAAsB,CAAC,GAAG;gBAC5B,wCAAwC;gBACxC,iBAAiB,IAAI,CAAC;oBACpB,GAAG,OAAO;oBACV,aAAa;gBACf;YACF,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,eAAe,gBAAgB,CAAC,kBAAkB;gBACxD,IAAI,QAAQ,WAAW,KAAK,GAAG;oBAC7B,gBAAgB,CAAC,kBAAkB,GAAG;wBACpC,GAAG,YAAY;wBACf,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC;oBAC3D;gBACF,OAAO;oBACL,gBAAgB,CAAC,kBAAkB,GAAG;wBACpC,GAAG,YAAY;wBACf,UAAU,CAAC,aAAa,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC;oBACjE;gBACF;YACF;YAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IACE,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,iBAAiB,EAAE;QACrE,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC,OACtC,KAAK,WAAW,KAAK,cACjB;gBAAE,GAAG,IAAI;gBAAE,UAAU,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;YAAE,IAC9C;QAEN,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,aAAa;IACf;IAEA,MAAM,yBAAyB,CAAC;QAC9B,aAAa,CAAC;YACZ,MAAM,mBAAmB,UACtB,GAAG,CAAC,CAAC;gBACJ,IAAI,KAAK,WAAW,KAAK,aAAa;oBACpC,MAAM,cAAc,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;oBAC3C,OAAO,cAAc,IAAI;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAY,IAAI;gBAChE;gBACA,OAAO;YACT,GACC,MAAM,CAAC;YAEV,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa,CAAC;YACZ,MAAM,gBAAgB,cAAc,SAAS,CAC3C,CAAC,OAAS,KAAK,WAAW,KAAK;YAEjC,IAAI,kBAAkB,CAAC,GAAG,OAAO;YACjC,MAAM,mBAAmB;mBACpB,cAAc,KAAK,CAAC,GAAG;mBACvB,cAAc,KAAK,CAAC,gBAAgB;aACxC;YACD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YAEjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,gBAAgB,mBAAmB,GAAG,CAAC,KAAK,WAAW;QAC7D,IAAI,gBAAgB;QAEpB,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,gBAAgB,KAAK,MAAM,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,IAAI;QAEJ,IAAI,eAAe;YACjB,mBAAmB;mBACd;gBACH;oBACE,GAAG,IAAI;oBACP,aAAa;oBACb,cAAc;wBAAC,KAAK,WAAW;qBAAC;oBAChC,QAAQ;oBACR,OAAO,KAAK,UAAU,IAAI,KAAK,KAAK,IAAI;gBAC1C;aACD;YACD,mJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC;QACtE,OAAO;YACL,MAAM,oBAAoB,UAAU,SAAS,CAC3C,CAAC,WACC,SAAS,WAAW,KAAK,KAAK,WAAW,IAAI,CAAC,SAAS,WAAW;YAGtE,IAAI,oBAAoB,CAAC,GAAG;gBAC1B,mBAAmB,UAAU,GAAG,CAAC,CAAC,UAAU;oBAC1C,IAAI,UAAU,mBAAmB;wBAC/B,MAAM,cAAc,CAAC,SAAS,QAAQ,IAAI,CAAC,IAAI;wBAC/C,mJAAA,CAAA,QAAK,CAAC,IAAI,CACR,CAAC,QAAQ,EAAE,KAAK,WAAW,CAAC,aAAa,EAAE,aAAa;wBAE1D,OAAO;4BACL,GAAG,QAAQ;4BACX,UAAU;4BACV,OAAO,KAAK,KAAK,IAAI;wBACvB;oBACF;oBACA,OAAO;gBACT;YACF,OAAO;gBACL,mBAAmB;uBACd;oBACH;wBACE,GAAG,IAAI;wBACP,aAAa;wBACb,cAAc;4BAAC,KAAK,WAAW;yBAAC;wBAChC,UAAU;wBACV,OAAO,KAAK,KAAK,IAAI;oBACvB;iBACD;gBACD,mJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC;YAChD;QACF;QAEA,aAAa;QACb,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD;IAEA,MAAM,6BAA6B;QACjC,wBAAwB;IAC1B;IAEA,MAAM,8BAA8B;QAClC,wBAAwB;QACxB,kBAAkB;QAClB,mBAAmB;QACnB,sBAAsB;QACtB,uBAAuB;IACzB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;YACvC,SAAS;YACT;QACF;QAEA,MAAM,aAAa;YACjB,KAAK,KAAK,GAAG,GAAG,QAAQ;YACxB,aAAa;YACb,OAAO,WAAW;YAClB,UAAU;YACV,WAAW;QACb;QAEA,SAAS;eAAI;YAAO;SAAW;QAC/B,gBAAgB;QAChB;IACF;IAEA,MAAM,cAAc;QAClB;QACA,WAAW,IAAM,WAAW,QAAQ;IACtC;IAEA,gDAAgD;IAChD,MAAM,eAAe;QACnB,MAAM,YAAY;YAChB;gBACE,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAS;oBAAe;iBAAY;gBACnD,aAAa;YACf;YACA;gBACE,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAU;iBAAc;gBACvC,aAAa;YACf;YACA;gBACE,aAAa;gBACb,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,cAAc;oBAAC;oBAAQ;oBAAgB;iBAAa;gBACpD,aAAa;YACf;SACD;QAED,aAAa;QACb,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,kBAAkB;QACtB,aAAa,EAAE,GAAG,yBAAyB;QAC3C,aAAa,UAAU,CAAC,cAAc,2BAA2B;QACjE,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B,2BAA2B;IAC1E;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,mCAAmC,EAAE,mBAAmB,QAAQ;YAEnE,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,mBAAmB,KAAK,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC3C,MAAM,KAAK,WAAW;oBACtB,OAAO,KAAK,KAAK;gBACnB,CAAC;YAED,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,IAAI,MAAM,IAAI,OAAO,IAAI;YACvB,kBAAkB,EAAE;YACpB;QACF;QACA,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,aAAa;IACf;IAEA,MAAM,qBAAqB,CAAC,aAAa;QACvC,aAAa,CAAC;YACZ,MAAM,eAAe,UAAU,GAAG,CAAC,CAAC;gBAClC,IAAI,KAAK,WAAW,KAAK,aAAa;oBACpC,MAAM,SAAS,KAAK,GAAG,CACrB,GACA,KAAK,KAAK,CAAC,WAAW,aAAa,KAAK,QAAQ;oBAElD,OAAO;wBACL,GAAG,IAAI;wBACP,QAAQ;oBACV;gBACF;gBACA,OAAO;YACT;YAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,iBAAc;wDAAE,WAAU;;;;;;oDAAuB;oDAC5D,UAAU,MAAM;oDAAC;;;;;;;0DAE/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS,IAAM,oBAAoB;wDACnC,SAAQ;wDACR,MAAK;wDACL,yBAAW,8OAAC,mKAAA,CAAA,UAAkB;;;;;kEAC/B;;;;;;kEAGD,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS,IAAM,wBAAwB;wDACvC,SAAQ;wDACR,MAAK;wDACL,yBAAW,8OAAC,gKAAA,CAAA,UAAe;;;;;kEAC5B;;;;;;kEAGD,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAQ;wDACR,OAAM;wDACN,MAAK;kEACN;;;;;;;;;;;;;;;;;;oCAMJ,UAAU,MAAM,KAAK,mBACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,iBAAc;gDAAE,WAAU;;;;;;0DACjD,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC,0LAAA,CAAA,SAAM;gDACL,SAAS,IAAM,oBAAoB;gDACnC,SAAQ;gDACR,WAAU;gDACV,yBAAW,8OAAC,mKAAA,CAAA,UAAkB;;;;;0DAC/B;;;;;;;;;;;;oCAOJ,UAAU,MAAM,GAAG,mBAClB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,yMAAA,CAAA,cAAW;wEAAC,MAAK;wEAAQ,WAAU;kFAClC,cAAA,8OAAC,0LAAA,CAAA,SAAM;4EACL,OAAO,KAAK,eAAe,IAAI,KAAK,WAAW;4EAC/C,UAAU,CAAC,IACT,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAE5C,YAAY;4EACZ,WAAU;sFAET,KAAK,YAAY,IAChB,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBAClC,8OAAC,gMAAA,CAAA,WAAQ;oFAAS,OAAO;8FACtB;mFADY;;;;;;;;;;;;;;;;;;;;8EAOzB,8OAAC,6JAAA,CAAA,UAAU;oEACT,SAAS,IAAM,gBAAgB,KAAK,WAAW;oEAC/C,MAAK;oEACL,WAAU;8EAEV,cAAA,8OAAC,2JAAA,CAAA,UAAU;wEAAC,UAAS;;;;;;;;;;;;;;;;;sEAIzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,KAAK,WAAW,KAAK,kBACpB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,8OAAC,mMAAA,CAAA,YAAS;gFACR,MAAK;gFACL,OAAO,WAAW,KAAK,MAAM,IAAI;gFACjC,UAAU,CAAC,IACT,mBACE,KAAK,WAAW,EAChB,EAAE,MAAM,CAAC,KAAK;gFAGlB,MAAK;gFACL,WAAU;gFACV,WAAW;oFACT,OAAO;wFACL,4BACE,8OAAC;4FAAK,WAAU;sGAAwB;;;;;;wFAE1C,YAAY;4FAAE,KAAK;4FAAG,MAAM;wFAAM;oFACpC;gFACF;;;;;;;;;;;6FAIJ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,6JAAA,CAAA,UAAU;wFACT,SAAS,IACP,uBAAuB,KAAK,WAAW;wFAEzC,MAAK;wFACL,WAAU;kGAEV,cAAA,8OAAC,2JAAA,CAAA,UAAU;4FAAC,UAAS;;;;;;;;;;;kGAEvB,8OAAC;wFAAK,WAAU;kGACb,KAAK,QAAQ,IAAI;;;;;;kGAEpB,8OAAC,6JAAA,CAAA,UAAU;wFACT,SAAS,IACP,uBAAuB,KAAK,WAAW;wFAEzC,MAAK;wFACL,WAAU;kGAEV,cAAA,8OAAC,wJAAA,CAAA,UAAO;4FAAC,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAM5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFAAwB;gFACnC,KAAK,KAAK;gFAAC;gFAAE,KAAK,WAAW,KAAK,IAAI,QAAQ;;;;;;;sFAElD,8OAAC;4EAAI,WAAU;;gFAAsC;gFACjD,CACA,KAAK,KAAK,GACV,CAAC,KAAK,WAAW,KAAK,IAClB,KAAK,MAAM,GACX,KAAK,QAAQ,CACnB,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;mDAjGX;;;;;;;;;;;;;;;;;;;;;0CA6GjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmE;;;;;;0DACjF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAQ;wDACR,OAAM;wDACN,MAAK;wDACL,yBAAW,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,iBAAc;;;;;;wDAChD,WAAU;kEACX;;;;;;kEAGD,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS,IAAM,oBAAoB;wDACnC,SAAQ;wDACR,MAAK;wDACL,yBAAW,8OAAC,mKAAA,CAAA,UAAkB;;;;;wDAC9B,WAAU;kEACX;;;;;;kEAGD,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS,IAAM,wBAAwB;wDACvC,SAAQ;wDACR,MAAK;wDACL,yBAAW,8OAAC,gKAAA,CAAA,UAAe;;;;;wDAC3B,WAAU;kEACX;;;;;;kEAGD,8OAAC,0LAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,OAAM;kEACP;;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmE;;;;;;0DACjF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAe,UAAU,MAAM;;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAAc;oEAAE;;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EACb,OAAO,aAAa,cAAc,CAAC,SAAS;wEAC3C,OAAO;wEACP,UAAU;wEACV,uBAAuB;wEACvB,uBAAuB;oEACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,UAAU,UAAU,MAAM,KAAK;0DAChC;;;;;;0DAGD,8OAAC,0LAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,UAAU,UAAU,MAAM,KAAK;0DAChC;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0LAAA,CAAA,SAAM;wBACL,MAAM;wBACN,SAAS,IAAM,oBAAoB;wBACnC,SAAS;wBACT,UAAS;;0CAET,8OAAC,yMAAA,CAAA,cAAW;;oCAAC;kDAEX,8OAAC,6JAAA,CAAA,UAAU;wCACT,MAAK;wCACL,OAAM;wCACN,SAAS,IAAM,oBAAoB;wCACnC,cAAW;wCACX,IAAI;4CAAE,UAAU;4CAAY,OAAO;4CAAI,KAAK;wCAAG;kDAE/C,cAAA,8OAAC,2JAAA,CAAA,UAAU;;;;;;;;;;;;;;;;0CAGf,8OAAC,+MAAA,CAAA,gBAAa;;kDACZ,8OAAC,mMAAA,CAAA,YAAS;wCACR,SAAS;wCACT,SAAQ;wCACR,aAAY;wCACZ,OAAO;wCACP,UAAU;wCACV,IAAI;4CAAE,IAAI;wCAAE;;;;;;oCAEb,eAAe,MAAM,GAAG,kBACvB,8OAAC,oLAAA,CAAA,OAAI;kDACF,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,gMAAA,CAAA,WAAQ;gDAEP,MAAM;gDACN,SAAS;oDACP,gBAAgB;oDAChB,oBAAoB;gDACtB;gDACA,IAAI;oDACF,SAAS;oDACT,gBAAgB;oDAChB,cAAc;oDACd,SAAS;gDACX;;kEAEA,8OAAC,sMAAA,CAAA,aAAU;kEAAE,KAAK,IAAI;;;;;;kEACtB,8OAAC,sMAAA,CAAA,aAAU;wDACT,IAAI;4DACF,YAAY;4DACZ,OAAO;4DACP,IAAI;wDACN;;4DACD;4DACG,KAAK,KAAK;;;;;;;;+CArBT;;;;;;;;;6DA2BX,8OAAC,sMAAA,CAAA,aAAU;wCACT,IAAI;4CAAE,WAAW;4CAAU,IAAI;4CAAG,OAAO;wCAAO;kDACjD;;;;;;;;;;;;;;;;;;kCAOP,8OAAC,0LAAA,CAAA,SAAM;wBACL,MAAM;wBACN,SAAS;;0CAET,8OAAC,yMAAA,CAAA,cAAW;0CAAC;;;;;;0CACb,8OAAC,+MAAA,CAAA,gBAAa;;kDACZ,8OAAC,mMAAA,CAAA,YAAS;wCACR,IAAG;wCACH,SAAQ;wCACR,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,IAAI;4CAAE,IAAI;4CAAG,OAAO;wCAAO;wCAC3B,QAAQ;;;;;;kDAEV,8OAAC,mMAAA,CAAA,YAAS;wCACR,IAAG;wCACH,SAAQ;wCACR,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,IAAI;4CAAE,IAAI;4CAAG,OAAO;wCAAO;wCAC3B,QAAQ;;;;;;;;;;;;0CAGZ,8OAAC,+MAAA,CAAA,gBAAa;;kDACZ,8OAAC,0LAAA,CAAA,SAAM;wCAAC,SAAS;kDAA6B;;;;;;kDAC9C,8OAAC,0LAAA,CAAA,SAAM;wCACL,SAAS;wCACT,OAAM;wCACN,SAAQ;kDACT;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAO,KAAK;wBAAW,OAAO;4BAAE,SAAS;wBAAO;;;;;;oBAGhD,iCACC,8OAAC;wBAAI,WAAU;kCAAiF;;;;;;oBAIjG,+BACC,8OAAC;wBAAI,WAAU;kCAAkF;;;;;;;;;;;;;;;;;;AAO3G;uCAEe"}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2380, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/app/cart/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport Cart from \"@/components/cart/cart\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nexport default function CartPage() {\n  const [totalPrice, setTotalPrice] = useState(0);\n\n  const handleUpdateTotalPrice = (price: number) => {\n    setTotalPrice(price);\n  };\n\n  return (\n    <div>\n      <Cart onUpdateTotalPrice={handleUpdateTotalPrice} />\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,qBACE,8OAAC;;0BACC,8OAAC,kIAAA,CAAA,UAAI;gBAAC,oBAAoB;;;;;;0BAC1B,8OAAC,mJAAA,CAAA,iBAAc;gBACb,UAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,aAAa;gBACb,YAAY;gBACZ,KAAK;gBACL,gBAAgB;gBAChB,SAAS;gBACT,YAAY;gBACZ,OAAM;;;;;;;;;;;;AAId"}}, {"offset": {"line": 2430, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}