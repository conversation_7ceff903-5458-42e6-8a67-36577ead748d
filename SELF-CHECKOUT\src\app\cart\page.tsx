"use client";

import React, { useState } from "react";
import Cart from "@/components/cart/cart";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function CartPage() {
  const [totalPrice, setTotalPrice] = useState(0);

  const handleUpdateTotalPrice = (price: number) => {
    setTotalPrice(price);
  };

  return (
    <div>
      <Cart onUpdateTotalPrice={handleUpdateTotalPrice} />
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
}
