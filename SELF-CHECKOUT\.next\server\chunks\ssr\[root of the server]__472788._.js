module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/cart/header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
;
;
const Header = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-20 sticky top-0 flex justify-center items-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            src: '/Logo.svg',
            alt: '',
            width: 200,
            height: 100
        }, void 0, false, {
            fileName: "[project]/src/components/cart/header.tsx",
            lineNumber: 6,
            columnNumber: 80
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart/header.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Header;
}}),
"[project]/src/components/cart/cart.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// "use client";
// import React, { useRef, useState, useEffect } from "react";
// import { Typography, Box, Divider, List } from "@mui/material";
// import Header from "./header";
// interface ROI {
//   x: number;
//   y: number;
//   width: number;
//   height: number;
// }
// interface CartProps {
//   onUpdateTotalPrice: (price: number) => void;
// }
// const Cart: React.FC<CartProps> = ({ onUpdateTotalPrice }) => {
//   const canvasRef = useRef<HTMLCanvasElement | null>(null);
//   const [roi] = useState<ROI>({
//     x: 208,
//     y: 175,
//     width: 465 - 208,
//     height: 512 - 175,
//   });
//   const [cartItems, setCartItems] = useState<{ name: string; price: number }[]>(
//     []
//   );
//   useEffect(() => {
//     const totalAmount = cartItems.reduce((acc, item) => acc + item.price, 0);
//     onUpdateTotalPrice(totalAmount); // Notify parent of the updated total price
//   }, [cartItems, onUpdateTotalPrice]);
//   // Fetch price based on weight_flag and productName
//   const fetchProductPrice = async (productName: string, weightFlag: number) => {
//     const endpoint = weightFlag === 0 ? "pricePer" : "pricePerKg";
//     try {
//       const response = await fetch(
//         `http://192.168.1.26:5000/${endpoint}?productName=${encodeURIComponent(
//           productName
//         )}`
//       );
//       if (response.ok) {
//         const data = await response.json();
//         return weightFlag === 0 ? data.pricePer : data.pricePerKg;
//       } else {
//         console.error(`Failed to fetch price for ${productName}`);
//         return 0;
//       }
//     } catch (error) {
//       console.error("Error fetching product price:", error);
//       return 0;
//     }
//   };
//   // Call predict API and update cart items
//   const handleMotionDetection = async () => {
//     try {
//       if (!canvasRef.current) return;
//       const canvas = canvasRef.current;
//       const blob = await new Promise<Blob | null>((resolve) =>
//         canvas.toBlob(resolve, "image/png")
//       );
//       if (!blob) return;
//       const formData = new FormData();
//       formData.append("image", blob, "capture.png");
//       const response = await fetch("http://192.168.1.6:9000/predict", {
//         method: "POST",
//         body: formData,
//       });
//       if (response.ok) {
//         const data = await response.json();
//         console.log("Predict API Response:", data);
//         const { yolo_tag, weight_flag } = data;
//         // Fetch prices for each product in yolo_tag
//         const updatedCartItems = await Promise.all(
//           yolo_tag.map(async (productName: string) => {
//             const price = await fetchProductPrice(productName, weight_flag);
//             return { name: productName, price };
//           })
//         );
//         setCartItems(updatedCartItems);
//       } else {
//         console.error("Failed to fetch prediction data from API");
//       }
//     } catch (error) {
//       console.error("Error during motion handling:", error);
//     }
//   };
//   // Motion detection logic
//   useEffect(() => {
//     const cellSize = 30;
//     const diffThreshold = 10000;
//     const processEveryNthFrame = 3;
//     let frameCounter = 0;
//     let previousGrayData: Uint8ClampedArray | null = null;
//     const fetchAndProcessImage = async () => {
//       try {
//         const response = await fetch("http://************:9000/image");
//         console.log("response", response);
//         const blob = await response.blob();
//         const url = URL.createObjectURL(blob);
//         const image = new Image();
//         console.log("image function call");
//         image.src = url;
//         console.log(image.src);
//         image.onload = () => {
//           console.log(onload);
//           if (canvasRef.current) {
//             const canvas = canvasRef.current;
//             const ctx = canvas.getContext("2d");
//             if (!ctx) return;
//             canvas.width = image.width;
//             canvas.height = image.height;
//             ctx.clearRect(0, 0, canvas.width, canvas.height);
//             ctx.drawImage(image, 0, 0);
//             frameCounter++;
//             if (frameCounter % processEveryNthFrame !== 0) {
//               URL.revokeObjectURL(url);
//               return;
//             }
//             const { x: roiX, y: roiY, width, height } = roi;
//             const currentImageData = ctx.getImageData(
//               roiX,
//               roiY,
//               width,
//               height
//             );
//             const currentGrayData = toGrayscale(currentImageData);
//             if (previousGrayData) {
//               const motionCells = detectMotion(
//                 previousGrayData,
//                 currentGrayData,
//                 width,
//                 height,
//                 cellSize,
//                 diffThreshold
//               );
//               console.log("motion length", motionCells.length);
//               if (motionCells.length > 0) {
//                 handleMotionDetection(); // Call predict API on motion detection
//               }
//             }
//             previousGrayData = currentGrayData;
//             URL.revokeObjectURL(url);
//           }
//         };
//       } catch (error) {
//         console.error("Error fetching or processing image:", error);
//       }
//     };
//     const interval = setInterval(fetchAndProcessImage, 250);
//     return () => clearInterval(interval);
//   }, [roi]);
//   const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {
//     const { data, width, height } = imageData;
//     const grayData = new Uint8ClampedArray(width * height);
//     for (let i = 0; i < data.length; i += 4) {
//       const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
//       grayData[i / 4] = gray;
//     }
//     return grayData;
//   };
//   const detectMotion = (
//     prevGray: Uint8ClampedArray,
//     currentGray: Uint8ClampedArray,
//     width: number,
//     height: number,
//     cellSize: number,
//     threshold: number
//   ): { x: number; y: number }[] => {
//     const motionCells: { x: number; y: number }[] = [];
//     for (let y = 0; y <= height - cellSize; y += cellSize) {
//       for (let x = 0; x <= width - cellSize; x += cellSize) {
//         let cellDiff = 0;
//         for (let i = y; i < y + cellSize; i++) {
//           for (let j = x; j < x + cellSize; j++) {
//             const index = i * width + j;
//             if (index < prevGray.length) {
//               cellDiff += Math.abs(prevGray[index] - currentGray[index]);
//             }
//           }
//         }
//         if (cellDiff > threshold) {
//           motionCells.push({ x, y });
//         }
//       }
//     }
//     return motionCells;
//   };
//   const totalAmount = cartItems
//     .reduce((acc, item) => acc + item.price, 0)
//     .toFixed(2);
//   return (
//     <div className="h-full bg-white p-1 flex flex-col items-center justify-center">
//       <Header />
//       <Box
//         sx={{
//           width: "100%",
//           maxWidth: "600px",
//           backgroundColor: "white",
//           borderRadius: "18px",
//           height: "90%",
//           boxShadow: 3,
//           p: 4,
//           display: "flex",
//           flexDirection: "column",
//         }}
//       >
//         <Box
//           display="flex"
//           justifyContent="space-between"
//           alignItems="center"
//           mb={2}
//         >
//           <Typography
//             variant="h5"
//             component="h1"
//             className="text-black font-semibold"
//           >
//             Your Cart
//           </Typography>
//         </Box>
//         <List sx={{ flexGrow: 1, overflowY: "auto" }}>
//           {cartItems.map((item, index) => (
//             <div
//               key={index}
//               className="bg-gray-100 rounded-md shadow-md p-6 mb-2 flex justify-between items-center"
//             >
//               <Typography variant="body1" fontWeight="medium">
//                 {item.name}
//               </Typography>
//               <Typography className="flex items-center space-x-4" component="div">
//                 <Typography
//                 component="span"
//                   variant="body1"
//                   fontWeight="medium"
//                   className="text-right"
//                 >
//                   ₹{item.price.toFixed(2)}
//                   <Typography variant="body2" color="textSecondary">
//                     x1
//                   </Typography>
//                 </Typography>
//               </Typography>
//             </div>
//           ))}
//         </List>
//         <Divider sx={{ my: 2 }} />
//         <Box display="flex" justifyContent="space-between" mb={2}>
//           <Typography variant="h6" fontWeight="bold">
//             Total:
//           </Typography>
//           <Typography variant="h6" fontWeight="bold">
//             ₹{totalAmount}
//           </Typography>
//         </Box>
//       </Box>
//       <canvas ref={canvasRef} style={{ display: "none" }} />
//       <div className=" mt-2 text-sm -mb-2"> Powered by Synecx AI</div>
//     </div>
//   );
// };
// export default Cart;
// "use client";
// import React, { useRef, useState, useEffect } from "react";
// import { Typography, Box, Divider, List } from "@mui/material";
// import Header from "./header";
// interface ROI {
//   x: number;
//   y: number;
//   width: number;
//   height: number;
// }
// interface CartProps {
//   onUpdateTotalPrice: (price: number) => void;
// }
// const Cart: React.FC<CartProps> = ({ onUpdateTotalPrice }) => {
//   const canvasRef = useRef<HTMLCanvasElement | null>(null);
//   const [roi] = useState<ROI>({
//     x: 208,
//     y: 175,
//     width: 465 - 208,
//     height: 512 - 175,
//   });
//   const [cartItems, setCartItems] = useState<{ name: string; price: number }[]>(
//     []
//   );
// useEffect(() => {
//   const totalAmount = cartItems.reduce((acc, item) => acc + item.price, 0);
//   onUpdateTotalPrice(totalAmount); // Notify parent of the updated total price
// }, [cartItems, onUpdateTotalPrice]);
//   // Fetch price based on weight_flag and productName
//   const fetchProductPrice = async (productName: string, weightFlag: number) => {
//     const endpoint = weightFlag === 0 ? "pricePer" : "pricePerKg";
//     try {
//       const response = await fetch(
//         `http://192.168.1.26:5000/${endpoint}?productName=${encodeURIComponent(
//           productName
//         )}`
//       );
//       if (response.ok) {
//         const data = await response.json();
//         return weightFlag === 0 ? data.pricePer : data.pricePerKg;
//       } else {
//         console.error(`Failed to fetch price for ${productName}`);
//         return 0;
//       }
//     } catch (error) {
//       console.error("Error fetching product price:", error);
//       return 0;
//     }
//   };
//   // Call predict API and update cart items
//   const handleMotionDetection = async () => {
//     try {
//       if (!canvasRef.current) return;
//       const canvas = canvasRef.current;
//       const blob = await new Promise<Blob | null>((resolve) =>
//         canvas.toBlob(resolve, "image/png")
//       );
//       if (!blob) return;
//       const formData = new FormData();
//       formData.append("image", blob, "capture.png");
//       const response = await fetch("http://***********:9000/predict", {
//         method: "POST",
//         body: formData,
//       });
//       if (response.ok) {
//         const data = await response.json();
//         console.log("Predict API Response:", data);
//         const { yolo_tag,clip_tag, weight_flag } = data;
//         // Fetch prices for each product in yolo_tag
//         const updatedCartItems = await Promise.all(
//           yolo_tag.map(async (productName: string) => {
//             const price = await fetchProductPrice(productName, weight_flag);
//             return { name: productName, price };
//           })
//         );
//         setCartItems(updatedCartItems);
//       } else {
//         console.error("Failed to fetch prediction data from API");
//       }
//     } catch (error) {
//       console.error("Error during motion handling:", error);
//     }
//   };
//   // Motion detection logic
//   useEffect(() => {
//     const cellSize = 30;
//     const diffThreshold = 10000;
//     const processEveryNthFrame = 3;
//     let frameCounter = 0;
//     let previousGrayData: Uint8ClampedArray | null = null;
//     const fetchAndProcessImage = async () => {
//       try {
//         const response = await fetch("http://************:9000/image");
//         console.log("response", response);
//         const blob = await response.blob();
//         const url = URL.createObjectURL(blob);
//         const image = new Image();
//         console.log("image function call");
//         image.src = url;
//         console.log(image.src);
//         image.onload = () => {
//           console.log(onload);
//           if (canvasRef.current) {
//             const canvas = canvasRef.current;
//             const ctx = canvas.getContext("2d");
//             if (!ctx) return;
//             canvas.width = image.width;
//             canvas.height = image.height;
//             ctx.clearRect(0, 0, canvas.width, canvas.height);
//             ctx.drawImage(image, 0, 0);
//             frameCounter++;
//             if (frameCounter % processEveryNthFrame !== 0) {
//               URL.revokeObjectURL(url);
//               return;
//             }
//             const { x: roiX, y: roiY, width, height } = roi;
//             const currentImageData = ctx.getImageData(
//               roiX,
//               roiY,
//               width,
//               height
//             );
//             const currentGrayData = toGrayscale(currentImageData);
//             if (previousGrayData) {
//               const motionCells = detectMotion(
//                 previousGrayData,
//                 currentGrayData,
//                 width,
//                 height,
//                 cellSize,
//                 diffThreshold
//               );
//               console.log("motion length", motionCells.length);
//               if (motionCells.length > 0) {
//                 handleMotionDetection(); // Call predict API on motion detection
//               }
//             }
//             previousGrayData = currentGrayData;
//             URL.revokeObjectURL(url);
//           }
//         };
//       } catch (error) {
//         console.error("Error fetching or processing image:", error);
//       }
//     };
//     const interval = setInterval(fetchAndProcessImage, 250);
//     return () => clearInterval(interval);
//   }, [roi]);
//   const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {
//     const { data, width, height } = imageData;
//     const grayData = new Uint8ClampedArray(width * height);
//     for (let i = 0; i < data.length; i += 4) {
//       const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
//       grayData[i / 4] = gray;
//     }
//     return grayData;
//   };
//   const detectMotion = (
//     prevGray: Uint8ClampedArray,
//     currentGray: Uint8ClampedArray,
//     width: number,
//     height: number,
//     cellSize: number,
//     threshold: number
//   ): { x: number; y: number }[] => {
//     const motionCells: { x: number; y: number }[] = [];
//     for (let y = 0; y <= height - cellSize; y += cellSize) {
//       for (let x = 0; x <= width - cellSize; x += cellSize) {
//         let cellDiff = 0;
//         for (let i = y; i < y + cellSize; i++) {
//           for (let j = x; j < x + cellSize; j++) {
//             const index = i * width + j;
//             if (index < prevGray.length) {
//               cellDiff += Math.abs(prevGray[index] - currentGray[index]);
//             }
//           }
//         }
//         if (cellDiff > threshold) {
//           motionCells.push({ x, y });
//         }
//       }
//     }
//     return motionCells;
//   };
//   const totalAmount = cartItems
//     .reduce((acc, item) => acc + item.price, 0)
//     .toFixed(2);
//   return (
//     <div className="h-full bg-white p-1 flex flex-col items-center justify-center">
//       <Header />
//       <Box
//         sx={{
//           width: "100%",
//           maxWidth: "600px",
//           backgroundColor: "white",
//           borderRadius: "18px",
//           height: "90%",
//           boxShadow: 3,
//           p: 4,
//           display: "flex",
//           flexDirection: "column",
//         }}
//       >
//         <Box
//           display="flex"
//           justifyContent="space-between"
//           alignItems="center"
//           mb={2}
//         >
//           <Typography
//             variant="h5"
//             component="h1"
//             className="text-black font-semibold"
//           >
//             Your Cart
//           </Typography>
//         </Box>
//         <List sx={{ flexGrow: 1, overflowY: "auto" }}>
//           {cartItems.map((item, index) => (
//             <div
//               key={index}
//               className="bg-gray-100 rounded-md shadow-md p-6 mb-2 flex justify-between items-center"
//             >
//               <Typography variant="body1" fontWeight="medium">
//                 {item.name}
//               </Typography>
//               <Typography className="flex items-center space-x-4" component="div">
//                 <Typography
//                 component="span"
//                   variant="body1"
//                   fontWeight="medium"
//                   className="text-right"
//                 >
//                   ₹{item.price.toFixed(2)}
//                   <Typography variant="body2" color="textSecondary">
//                     x1
//                   </Typography>
//                 </Typography>
//               </Typography>
//             </div>
//           ))}
//         </List>
//         <Divider sx={{ my: 2 }} />
//         <Box display="flex" justifyContent="space-between" mb={2}>
//           <Typography variant="h6" fontWeight="bold">
//             Total:
//           </Typography>
//           <Typography variant="h6" fontWeight="bold">
//             ₹{totalAmount}
//           </Typography>
//         </Box>
//       </Box>
//       <canvas ref={canvasRef} style={{ display: "none" }} />
//       <div className=" mt-2 text-sm -mb-2"> Powered by Synecx AI</div>
//     </div>
//   );
// };
// export default Cart;
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/cart/header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Button/Button.js [app-ssr] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/SearchOutlined.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/AddOutlined.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/FormControl/FormControl.js [app-ssr] (ecmascript) <export default as FormControl>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Select/Select.js [app-ssr] (ecmascript) <export default as Select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/MenuItem/MenuItem.js [app-ssr] (ecmascript) <export default as MenuItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/material/IconButton/IconButton.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Delete.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Remove.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Add$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Add.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/TextField/TextField.js [app-ssr] (ecmascript) <export default as TextField>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Dialog/Dialog.js [app-ssr] (ecmascript) <export default as Dialog>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogTitle/DialogTitle.js [app-ssr] (ecmascript) <export default as DialogTitle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogContent/DialogContent.js [app-ssr] (ecmascript) <export default as DialogContent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$List$2f$List$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/List/List.js [app-ssr] (ecmascript) <export default as List>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/ListItem/ListItem.js [app-ssr] (ecmascript) <export default as ListItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Typography/Typography.js [app-ssr] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogActions$2f$DialogActions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogActions$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogActions/DialogActions.js [app-ssr] (ecmascript) <export default as DialogActions>");
;
;
;
;
;
;
;
;
;
;
;
;
;
const WEIGHT_BASED_ITEMS = new Set([
    "Milk Assorted",
    "Normal Assorted",
    "Ghee Assorted",
    "Kaju Assorted",
    "ARISI-MURUKKU",
    "BOMBAY-MIXTURE",
    "GARLIC-MIXTURE",
    "KAARA-BOONTHI",
    "KAARA-MURUKKU",
    "KAI-SUTHU-MURUKKU",
    "KARA-SEV",
    "MASALA KADALAI",
    "MASALA-POTATO-CHIPS-GREEN-",
    "MASALA-POTATO-CHIPS-RED-",
    "NAENDHRAM-CHIPS",
    "NORMAL-MIXTURE",
    "OTTU-PAKKODA",
    "POTATO-CHIPS",
    "PUDI-MURUKKU",
    "THATTA-MURUKKU",
    "CORN",
    "BADHAM-BARFI",
    "BADHUSHA",
    "BANARAS-SANDWICH",
    "BESAN-LADDU",
    "BOMBAY-HALWA",
    "CARROT-MYSORE-PAK",
    "CHANDRAKALA",
    "DRY-FRUIT-LADDU",
    "GHEE-MYSORE-PAK",
    "GULAB-JAMUN",
    "GULKAN-BARFI",
    "HORLICKS-BARFI",
    "JILAPI",
    "KAJU-KATLI",
    "KAJU-PISTHA-ROLL",
    "KALA-JAMUN",
    "KALAKAN-BARFI",
    "LADDU",
    "LAMBA-JAMUN",
    "MAKAN-PEDA",
    "MANGO-KATLI",
    "MILK-CAKE",
    "MILK-PEDA",
    "MOTHI-LADDU",
    "MOTHI-PAK",
    "MYSORE-PAK",
    "RASGULLA",
    "SPECIAL-GHEE-SOANPAPDI"
]);
const Cart = ({ onUpdateTotalPrice })=>{
    const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [displayedItems, setDisplayedItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [cartItems, setCartItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [openAddProductDialog, setOpenAddProductDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [newProductName, setNewProductName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [newProductPrice, setNewProductPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [newProductQuantity, setNewProductQuantity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(1);
    const [newProductShortcode, setNewProductShortcode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [weight, setWeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [clicked, setClicked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchOpen, setSearchOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [openSearchDialog, setOpenSearchDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [clip_tag, setClipTag] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Motion Detection State and Refs
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [roi, setRoi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        x: 210,
        y: 159,
        width: 257,
        height: 337
    });
    const [showMotionToast, setShowMotionToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showItemToast, setShowItemToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const motionDetectedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const framesSinceMotionRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const cooldownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const [itemPlaced, setItemPlaced] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false); // New state for item placement
    // Calculate total price
    const calculateTotalPrice = (items)=>{
        return items.reduce((acc, item)=>{
            if (item.weight_flag === 1) {
                return acc + item.price * (item.weight || 0);
            }
            return acc + item.price * (item.quantity || 1);
        }, 0);
    };
    // Update total price whenever cart items change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const totalPrice = calculateTotalPrice(cartItems);
        if (onUpdateTotalPrice) {
            onUpdateTotalPrice(totalPrice);
        }
    }, [
        cartItems,
        onUpdateTotalPrice
    ]);
    // useEffect(() => {
    //   const cellSize = 30;
    //   const diffThreshold = 10000;
    //   const processEveryNthFrame = 3;
    //   let frameCounter = 0;
    //   let previousGrayData = null;
    //   const fetchAndProcessImage = async () => {
    //     try {
    //       const response = await fetch("http://*************:9000/image");
    //       const blob = await response.blob();
    //       const url = URL.createObjectURL(blob);
    //       // const image = new Image();
    //       const image = new window.Image();
    //       image.src = url;
    //       image.onload = () => {
    //         const canvas = canvasRef.current;
    //         const ctx = canvas.getContext("2d");
    //         canvas.width = image.width;
    //         canvas.height = image.height;
    //         ctx.clearRect(0, 0, canvas.width, canvas.height);
    //         ctx.drawImage(image, 0, 0);
    //         frameCounter++;
    //         if (frameCounter % processEveryNthFrame !== 0) {
    //           URL.revokeObjectURL(url);
    //           return;
    //         }
    //         const { x: roiX, y: roiY, width, height } = roi;
    //         const currentImageData = ctx.getImageData(roiX, roiY, width, height);
    //         const currentGrayData = toGrayscale(currentImageData);
    //         if (previousGrayData) {
    //           const motionCells = detectMotion(
    //             previousGrayData,
    //             currentGrayData,
    //             width,
    //             height,
    //             cellSize,
    //             diffThreshold
    //           );
    //           const motionDetectedNow = motionCells.length > 0;
    //           if (motionDetectedNow && !cooldownRef.current) {
    //             setShowMotionToast(true);
    //             motionDetectedRef.current = true;
    //             framesSinceMotionRef.current = 0;
    //             cooldownRef.current = true;
    //             setTimeout(() => {
    //               setShowMotionToast(false);
    //               cooldownRef.current = false;
    //             }, 500);
    //           } else if (motionDetectedRef.current) {
    //             framesSinceMotionRef.current += 1;
    //             if (framesSinceMotionRef.current >= 2) {
    //               setShowItemToast(true);
    //               motionDetectedRef.current = false;
    //               framesSinceMotionRef.current = 0;
    //               setItemPlaced(true); // Set itemPlaced to true
    //               setTimeout(() => {
    //                 setShowItemToast(false);
    //               }, 1000);
    //             }
    //           }
    //         }
    //         previousGrayData = currentGrayData;
    //         URL.revokeObjectURL(url);
    //       };
    //     } catch (error) {
    //       console.error("Error fetching or processing image:", error);
    //     }
    //   };
    //   const interval = setInterval(fetchAndProcessImage, 250);
    //   return () => clearInterval(interval);
    // }, [roi]);
    //saas 
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const cellSize = 20;
        const diffThreshold = 12500;
        const processEveryNthFrame = 1;
        let frameCounter = 0;
        let previousGrayData = null;
        //   
        const fetchAndProcessImage = async ()=>{
            try {
                const response = await fetch("http://*************:9000/image");
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                // const image = new Image();
                const image = new window.Image();
                image.src = url;
                image.onload = ()=>{
                    const canvas = canvasRef.current;
                    const ctx = canvas.getContext("2d");
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(image, 0, 0);
                    frameCounter++;
                    if (frameCounter % processEveryNthFrame !== 0) {
                        URL.revokeObjectURL(url);
                        return;
                    }
                    const { x: roiX, y: roiY, width, height } = roi;
                    const currentImageData = ctx.getImageData(roiX, roiY, width, height);
                    const currentGrayData = toGrayscale(currentImageData);
                    if (previousGrayData) {
                        const motionCells = detectMotion(previousGrayData, currentGrayData, width, height, cellSize, diffThreshold);
                        const motionDetectedNow = motionCells.length > 0;
                        if (motionDetectedNow && !cooldownRef.current) {
                            setShowMotionToast(true);
                            motionDetectedRef.current = true;
                            cooldownRef.current = true;
                            // Immediately call the prediction API
                            captureAndSendImage();
                            setTimeout(()=>{
                                setShowMotionToast(false);
                                cooldownRef.current = false;
                            }, 300);
                        }
                    }
                    previousGrayData = currentGrayData;
                    URL.revokeObjectURL(url);
                };
            } catch (error) {
                console.error("Error fetching or processing image:", error);
            }
        };
        const interval = setInterval(fetchAndProcessImage, 250);
        return ()=>clearInterval(interval);
    }, [
        roi
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (itemPlaced) {
            captureAndSendImage();
            setItemPlaced(false); // Reset itemPlaced after prediction
        }
    }, [
        itemPlaced
    ]);
    const toGrayscale = (imageData)=>{
        const { data, width, height } = imageData;
        const grayData = new Uint8ClampedArray(width * height);
        for(let i = 0; i < data.length; i += 4){
            const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
            grayData[i / 4] = gray;
        }
        return grayData;
    };
    const detectMotion = (prevGray, currentGray, width, height, cellSize, threshold)=>{
        const motionCells = [];
        for(let y = 0; y <= height - cellSize; y += cellSize){
            for(let x = 0; x <= width - cellSize; x += cellSize){
                let cellDiff = 0;
                for(let i = y; i < y + cellSize; i++){
                    for(let j = x; j < x + cellSize; j++){
                        const index = i * width + j;
                        if (index < prevGray.length) {
                            cellDiff += Math.abs(prevGray[index] - currentGray[index]);
                        }
                    }
                }
                if (cellDiff > threshold) {
                    motionCells.push({
                        x,
                        y
                    });
                }
            }
        }
        return motionCells;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, []);
    const subtotal = cartItems.reduce((acc, item)=>{
        if (item.weight_flag === 1) {
            return acc + item.price * (item.weight || 0);
        }
        return acc + item.price * (item.quantity || 1);
    }, 0).toFixed(2);
    const totalAmount = Math.round(parseFloat(subtotal));
    const handleDropdownChange = async (index, newProductName)=>{
        try {
            const isWeightBased = WEIGHT_BASED_ITEMS.has(newProductName);
            const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
            const priceResponse = await fetch(`http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(newProductName)}`);
            if (!priceResponse.ok) {
                throw new Error(`Failed to fetch price for ${newProductName}: ${priceResponse.statusText}`);
            }
            const priceData = await priceResponse.json();
            const newPrice = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
            setCartItems((prevCartItems)=>{
                const updatedCartItems = prevCartItems.map((item, i)=>{
                    if (i === index) {
                        const updatedItem = {
                            ...item,
                            selectedProduct: newProductName,
                            productName: newProductName,
                            price: newPrice,
                            weight_flag: isWeightBased ? 1 : 0
                        };
                        if (item.weight_flag !== (isWeightBased ? 1 : 0)) {
                            if (isWeightBased) {
                                delete updatedItem.quantity;
                                updatedItem.weight = 0;
                            } else {
                                delete updatedItem.weight;
                                updatedItem.quantity = 1;
                            }
                        }
                        return updatedItem;
                    }
                    return item;
                });
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
        } catch (error) {
            console.error("Error updating product price:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to update product price");
        }
    };
    // 
    // const captureAndSendImage = useCallback(async () => {
    //   try {
    //     toast.info("Capturing image...", { autoClose: 800 });
    //     const response = await fetch("http://*************:9000/image");
    //     if (!response.ok) {
    //       throw new Error(`Image capture failed: ${response.statusText}`);
    //     }
    //     const blob = await response.blob();
    //     const formData = new FormData();
    //     formData.append("image", blob, "raspberrypi.jpg");
    //     toast.info("Sending image for prediction...", { autoClose: 800 });
    //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
    //       method: "POST",
    //       body: formData,
    //     });
    //     if (!predictResponse.ok) {
    //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
    //     }
    //     const data = await predictResponse.json();
    //     console.log(data);
    //     const { yolo_tag, clip_tag, weight_flag } = data;
    //     const weightResponse = await fetch("http://*************:9000/weight");
    //     if (!weightResponse.ok) {
    //       throw new Error("Failed to fetch weight");
    //     }
    //     const weightData = await weightResponse.json();
    //     const weight = weightData.weight;
    //     setClipTag(clip_tag);
    //     console.log("Clip Tag: ", clip_tag);
    //     // Update cart items based on prediction
    //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
    //     const newCartItems = await Promise.all(
    //       tags.map(async (productName, index) => {
    //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
    //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
    //         const priceResponse = await fetch(
    //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
    //         );
    //         if (!priceResponse.ok) {
    //           throw new Error(`Failed to fetch price for ${productName}`);
    //         }
    //         const priceData = await priceResponse.json();
    //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
    //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);
    //         return {
    //           productName,
    //           weight_flag: isWeightBased ? 1 : 0,
    //           alternatives: [productName, ...alternatives],
    //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
    //         };
    //       })
    //     );
    //     // Remove items not in the predict response
    //     setCartItems((prevCartItems) => {
    //       const updatedCartItems = prevCartItems.filter((item) =>
    //         tags.includes(item.productName)
    //       );
    //       // Add new items from the predict response
    //       newCartItems.forEach((newItem) => {
    //         const existingItemIndex = updatedCartItems.findIndex(
    //           (item) => item.productName === newItem.productName
    //         );
    //         if (existingItemIndex > -1) {
    //           const existingItem = updatedCartItems[existingItemIndex];
    //           if (newItem.weight_flag === 1) {
    //             updatedCartItems[existingItemIndex] = {
    //               ...existingItem,
    //               weight: (existingItem.weight || 0) + (newItem.weight || 0),
    //             };
    //           } else {
    //             updatedCartItems[existingItemIndex] = {
    //               ...existingItem,
    //               quantity: (existingItem.quantity || 1) + (newItem.quantity || 1),
    //             };
    //           }
    //         } else {
    //           updatedCartItems.push(newItem);
    //         }
    //       });
    //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    //       return updatedCartItems;
    //     });
    //     setWeight(weight);
    //   } catch (error) {
    //     console.error("Error capturing or sending image:", error);
    //   }
    // }, []);
    // const captureAndSendImage = useCallback(async () => {
    //   try {
    //     toast.info("Capturing image...", { autoClose: 800 });
    //     const response = await fetch("http://*************:9000/image");
    //     if (!response.ok) {
    //       throw new Error(`Image capture failed: ${response.statusText}`);
    //     }
    //     const blob = await response.blob();
    //     const formData = new FormData();
    //     formData.append("image", blob, "raspberrypi.jpg");
    //     toast.info("Sending image for prediction...", { autoClose: 800 });
    //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
    //       method: "POST",
    //       body: formData,
    //     });
    //     if (!predictResponse.ok) {
    //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
    //     }
    //     const data = await predictResponse.json();
    //     console.log(data);
    //     const { yolo_tag, clip_tag, weight_flag } = data;
    //     const weightResponse = await fetch("http://*************:9000/weight");
    //     if (!weightResponse.ok) {
    //       throw new Error("Failed to fetch weight");
    //     }
    //     const weightData = await weightResponse.json();
    //     const weight = weightData.weight;
    //     setClipTag(clip_tag);
    //     console.log("Clip Tag: ", clip_tag);
    //     // Process predicted items
    //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
    //     const newCartItems = await Promise.all(
    //       tags.map(async (productName, index) => {
    //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
    //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
    //         const priceResponse = await fetch(
    //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
    //         );
    //         if (!priceResponse.ok) {
    //           throw new Error(`Failed to fetch price for ${productName}`);
    //         }
    //         const priceData = await priceResponse.json();
    //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
    //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);
    //         return {
    //           productName,
    //           weight_flag: isWeightBased ? 1 : 0,
    //           alternatives: [productName, ...alternatives],
    //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
    //           fromPredict: true  // Mark items from prediction
    //         };
    //       })
    //     );
    //     // Update cart items
    //     setCartItems((prevCartItems) => {
    //       // Keep manually added items
    //       const manualItems = prevCartItems.filter(item => !item.fromPredict);
    //       // Merge predicted items with existing quantities/weights
    //       const predictedItems = newCartItems.map(newItem => {
    //         const existingItem = prevCartItems.find(
    //           item => item.productName === newItem.productName && item.fromPredict
    //         );
    //         if (existingItem) {
    //           // Preserve existing quantity/weight for previously predicted items
    //           if (newItem.weight_flag === 1) {
    //             return {
    //               ...newItem,
    //               weight: existingItem.weight
    //             };
    //           } else {
    //             return {
    //               ...newItem,
    //               quantity: existingItem.quantity
    //             };
    //           }
    //         }
    //         return newItem;
    //       });
    //       // Combine manual and predicted items
    //       const updatedCartItems = [...manualItems, ...predictedItems];
    //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    //       return updatedCartItems;
    //     });
    //     setWeight(weight);
    //   } catch (error) {
    //     console.error("Error capturing or sending image:", error);
    //   }
    // }, []);
    const captureAndSendImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info("Capturing image...", {
                autoClose: 800
            });
            const response = await fetch("http://*************:9000/image");
            if (!response.ok) {
                throw new Error(`Image capture failed: ${response.statusText}`);
            }
            const blob = await response.blob();
            const formData = new FormData();
            formData.append("image", blob, "raspberrypi.jpg");
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info("Sending image for prediction...", {
                autoClose: 800
            });
            const predictResponse = await fetch("http://*************:9000/predict", {
                method: "POST",
                body: formData
            });
            if (!predictResponse.ok) {
                throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
            }
            const data = await predictResponse.json();
            console.log(data);
            const { yolo_tag, clip_tag, weight_flag } = data;
            const weightResponse = await fetch("http://*************:9000/weight");
            if (!weightResponse.ok) {
                throw new Error("Failed to fetch weight");
            }
            const weightData = await weightResponse.json();
            const weight = weightData.weight;
            setClipTag(clip_tag);
            console.log("Clip Tag: ", clip_tag);
            // Process predicted items
            const tags = Array.isArray(yolo_tag) ? yolo_tag : [
                yolo_tag
            ];
            // Calculate product counts here, after tags is defined
            const productCounts = tags.reduce((acc, product)=>{
                acc[product] = (acc[product] || 0) + 1;
                return acc;
            }, {});
            const newCartItems = await Promise.all(tags.map(async (productName, index)=>{
                const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
                const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
                const priceResponse = await fetch(`http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`);
                if (!priceResponse.ok) {
                    throw new Error(`Failed to fetch price for ${productName}`);
                }
                const priceData = await priceResponse.json();
                const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
                const alternatives = clip_tag[index].filter((tag)=>tag !== productName);
                return {
                    productName,
                    weight_flag: isWeightBased ? 1 : 0,
                    alternatives: [
                        productName,
                        ...alternatives
                    ],
                    ...isWeightBased ? {
                        weight: (weight || 0) * productCounts[productName],
                        price: price || 0
                    } : {
                        quantity: productCounts[productName],
                        price: price || 0
                    },
                    fromPredict: true
                };
            }));
            // Update cart items
            setCartItems((prevCartItems)=>{
                // Keep manually added items
                const manualItems = prevCartItems.filter((item)=>!item.fromPredict);
                // Get unique predicted items to avoid duplicates
                const uniquePredictedItems = Array.from(new Set(newCartItems.map((item)=>item.productName))).map((productName)=>{
                    const item = newCartItems.find((item)=>item.productName === productName);
                    const existingItem = prevCartItems.find((prevItem)=>prevItem.productName === productName && prevItem.fromPredict);
                    if (existingItem) {
                        // Preserve existing quantity/weight for previously predicted items
                        if (item.weight_flag === 1) {
                            return {
                                ...item,
                                weight: existingItem.weight
                            };
                        } else {
                            return {
                                ...item,
                                quantity: existingItem.quantity
                            };
                        }
                    }
                    return item;
                });
                // Combine manual and predicted items
                const updatedCartItems = [
                    ...manualItems,
                    ...uniquePredictedItems
                ];
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
            setWeight(weight);
        } catch (error) {
            console.error("Error capturing or sending image:", error);
        }
    }, []);
    // Modified addProduct function
    const addProduct = (product)=>{
        setCartItems((prevCartItems)=>{
            const updatedCartItems = [
                ...prevCartItems
            ];
            const existingItemIndex = updatedCartItems.findIndex((item)=>item.productName === product.productName && !item.fromPredict);
            if (existingItemIndex === -1) {
                // Add new item without fromPredict flag
                updatedCartItems.push({
                    ...product,
                    fromPredict: false
                });
            } else {
                // Update existing manual item
                const existingItem = updatedCartItems[existingItemIndex];
                if (product.weight_flag === 1) {
                    updatedCartItems[existingItemIndex] = {
                        ...existingItem,
                        weight: (existingItem.weight || 0) + (product.weight || 0)
                    };
                } else {
                    updatedCartItems[existingItemIndex] = {
                        ...existingItem,
                        quantity: (existingItem.quantity || 1) + (product.quantity || 1)
                    };
                }
            }
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleIncreaseQuantity = (productName)=>{
        const cartItems = JSON.parse(localStorage.getItem("cartItems")) || [];
        const updatedCartItems = cartItems.map((item)=>item.productName === productName ? {
                ...item,
                quantity: (item.quantity || 1) + 1
            } : item);
        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
        setCartItems(updatedCartItems);
    };
    const handleDecreaseQuantity = (productName)=>{
        setCartItems((prevItems)=>{
            const updatedCartItems = prevItems.map((item)=>{
                if (item.productName === productName) {
                    const newQuantity = (item.quantity || 1) - 1;
                    return newQuantity > 0 ? {
                        ...item,
                        quantity: newQuantity
                    } : null;
                }
                return item;
            }).filter(Boolean);
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleRemoveTag = (tagToRemove)=>{
        setCartItems((prevCartItems)=>{
            const indexToRemove = prevCartItems.findIndex((item)=>item.productName === tagToRemove);
            if (indexToRemove === -1) return prevCartItems;
            const updatedCartItems = [
                ...prevCartItems.slice(0, indexToRemove),
                ...prevCartItems.slice(indexToRemove + 1)
            ];
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleAddToCart = async (item)=>{
        const isWeightBased = WEIGHT_BASED_ITEMS.has(item.productName);
        let currentWeight = 0;
        if (isWeightBased) {
            try {
                const response = await fetch("http://************:9000/weight");
                if (response.ok) {
                    const data = await response.json();
                    currentWeight = data.weight || 0;
                }
            } catch (error) {
                console.error("Error fetching weight:", error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to get weight from scale");
                return;
            }
        }
        let updatedCartItems;
        if (isWeightBased) {
            updatedCartItems = [
                ...cartItems,
                {
                    ...item,
                    weight_flag: 1,
                    alternatives: [
                        item.productName
                    ],
                    weight: currentWeight,
                    price: item.pricePerKg || item.price || 0
                }
            ];
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info(`Added ${currentWeight}KG of ${item.productName} to cart`);
        } else {
            const existingItemIndex = cartItems.findIndex((cartItem)=>cartItem.productName === item.productName && !cartItem.weight_flag);
            if (existingItemIndex > -1) {
                updatedCartItems = cartItems.map((cartItem, index)=>{
                    if (index === existingItemIndex) {
                        const newQuantity = (cartItem.quantity || 1) + 1;
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info(`Updated ${item.productName} quantity to ${newQuantity}`);
                        return {
                            ...cartItem,
                            quantity: newQuantity,
                            price: item.price || 0
                        };
                    }
                    return cartItem;
                });
            } else {
                updatedCartItems = [
                    ...cartItems,
                    {
                        ...item,
                        weight_flag: 0,
                        alternatives: [
                            item.productName
                        ],
                        quantity: 1,
                        price: item.price || 0
                    }
                ];
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info(`Added ${item.productName} to cart`);
            }
        }
        setCartItems(updatedCartItems);
        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    };
    const handleOpenAddProductDialog = ()=>{
        setOpenAddProductDialog(true);
    };
    const handleCloseAddProductDialog = ()=>{
        setOpenAddProductDialog(false);
        setNewProductName("");
        setNewProductPrice("");
        setNewProductQuantity(1);
        setNewProductShortcode("");
    };
    const handleAddProductSubmit = ()=>{
        if (!newProductName || !newProductPrice) {
            setError("Please enter a product name and price.");
            return;
        }
        const newProduct = {
            _id: Date.now().toString(),
            productName: newProductName,
            price: parseFloat(newProductPrice),
            quantity: newProductQuantity,
            shortcode: newProductShortcode
        };
        setItems([
            ...items,
            newProduct
        ]);
        handleAddToCart(newProduct);
        handleCloseAddProductDialog();
    };
    const handleClick = ()=>{
        captureAndSendImage();
        setTimeout(()=>setClicked(false), 600);
    };
    // Demo function to add sample items for testing
    const addDemoItems = ()=>{
        const demoItems = [
            {
                productName: "Apple",
                price: 150,
                quantity: 2,
                weight_flag: 0,
                alternatives: [
                    "Apple",
                    "Green Apple",
                    "Red Apple"
                ],
                fromPredict: false
            },
            {
                productName: "Banana",
                price: 80,
                quantity: 3,
                weight_flag: 0,
                alternatives: [
                    "Banana",
                    "Ripe Banana"
                ],
                fromPredict: false
            },
            {
                productName: "Rice",
                price: 60,
                weight: 1.5,
                weight_flag: 1,
                alternatives: [
                    "Rice",
                    "Basmati Rice",
                    "Brown Rice"
                ],
                fromPredict: false
            }
        ];
        setCartItems(demoItems);
        localStorage.setItem("cartItems", JSON.stringify(demoItems));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Demo items added to cart!");
    };
    const handleClearCart = ()=>{
        setCartItems([]); // Clear cart items state
        localStorage.removeItem("cartItems"); // Remove from localStorage
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Cart cleared successfully!"); // Optional success message
    };
    const fetchSearchResults = async (query)=>{
        try {
            const response = await fetch(`http://localhost:5000/search?query=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error("Failed to fetch search results");
            const data = await response.json();
            const formattedResults = data.map((item)=>({
                    name: item.productName,
                    price: item.price
                }));
            setDisplayedItems(formattedResults);
        } catch (error) {
            console.error("Error fetching search results:", error);
        }
    };
    const handleSearch = (query)=>{
        setSearchQuery(query);
        if (query.trim() === "") {
            setDisplayedItems([]);
            return;
        }
        fetchSearchResults(query);
    };
    const handleInputChange = (e)=>{
        const query = e.target.value;
        setSearchQuery(query);
        handleSearch(query);
    };
    const handleWeightChange = (productName, newWeight)=>{
        setCartItems((prevItems)=>{
            const updatedItems = prevItems.map((item)=>{
                if (item.productName === productName) {
                    const weight = Math.max(0, Math.round(parseFloat(newWeight || 0) * 1000) / 1000);
                    return {
                        ...item,
                        weight: weight
                    };
                }
                return item;
            });
            localStorage.setItem("cartItems", JSON.stringify(updatedItems));
            return updatedItems;
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/cart/cart.jsx",
                lineNumber: 1605,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full max-w-full mx-auto px-4 py-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-140px)]",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-xl shadow-lg p-6 overflow-hidden flex flex-col",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                className: "text-2xl font-bold text-gray-800 flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faShoppingCart"],
                                                        className: "mr-3 text-blue-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1612,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Your Cart (",
                                                    cartItems.length,
                                                    " items)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1611,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: ()=>setOpenSearchDialog(true),
                                                        variant: "outlined",
                                                        size: "small",
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1620,
                                                            columnNumber: 30
                                                        }, void 0),
                                                        children: "Search"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1616,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: ()=>setOpenAddProductDialog(true),
                                                        variant: "outlined",
                                                        size: "small",
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1628,
                                                            columnNumber: 30
                                                        }, void 0),
                                                        children: "Add"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1624,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: handleClearCart,
                                                        variant: "outlined",
                                                        color: "error",
                                                        size: "small",
                                                        children: "Clear"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1632,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1615,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1610,
                                        columnNumber: 13
                                    }, this),
                                    cartItems.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1 flex flex-col items-center justify-center text-gray-500",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faShoppingCart"],
                                                className: "text-6xl mb-4 text-gray-300"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1645,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-xl font-semibold mb-2",
                                                children: "Your cart is empty"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1646,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-center",
                                                children: "Add some items to get started!"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1647,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                onClick: ()=>setOpenSearchDialog(true),
                                                variant: "contained",
                                                className: "mt-4",
                                                startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1652,
                                                    columnNumber: 30
                                                }, void 0),
                                                children: "Browse Products"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1648,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1644,
                                        columnNumber: 15
                                    }, this),
                                    cartItems.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1 overflow-hidden",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-full overflow-y-auto pr-2 space-y-3 custom-scrollbar",
                                            children: cartItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-start justify-between mb-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex-1 min-w-0",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__["FormControl"], {
                                                                        size: "small",
                                                                        className: "w-full max-w-xs",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__["Select"], {
                                                                            value: item.selectedProduct || item.productName,
                                                                            onChange: (e)=>handleDropdownChange(index, e.target.value),
                                                                            displayEmpty: true,
                                                                            className: "text-sm",
                                                                            children: item.alternatives && item.alternatives.map((alternative, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                                                                                    value: alternative,
                                                                                    children: alternative
                                                                                }, i, false, {
                                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                                    lineNumber: 1681,
                                                                                    columnNumber: 35
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1671,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1670,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1669,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    onClick: ()=>handleRemoveTag(item.productName),
                                                                    size: "small",
                                                                    className: "text-red-500 hover:bg-red-50",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        fontSize: "small"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1693,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1688,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1668,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-between",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-4",
                                                                    children: item.weight_flag === 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "flex items-center space-x-2",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "text-sm text-gray-600",
                                                                                children: "Weight:"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                                lineNumber: 1701,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                                                                type: "number",
                                                                                value: parseFloat(item.weight || 0),
                                                                                onChange: (e)=>handleWeightChange(item.productName, e.target.value),
                                                                                size: "small",
                                                                                className: "w-20",
                                                                                slotProps: {
                                                                                    input: {
                                                                                        endAdornment: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                            className: "text-xs text-gray-500",
                                                                                            children: "KG"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                                            lineNumber: 1716,
                                                                                            columnNumber: 39
                                                                                        }, void 0),
                                                                                        inputProps: {
                                                                                            min: 0,
                                                                                            step: 0.001
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                                lineNumber: 1702,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1700,
                                                                        columnNumber: 29
                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "flex items-center space-x-2",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "text-sm text-gray-600",
                                                                                children: "Qty:"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                                lineNumber: 1725,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "flex items-center space-x-1 bg-white rounded-lg border",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                        onClick: ()=>handleDecreaseQuantity(item.productName),
                                                                                        size: "small",
                                                                                        className: "text-red-500 hover:bg-red-50",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                            fontSize: "small"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                                            lineNumber: 1734,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1727,
                                                                                        columnNumber: 33
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: "px-3 py-1 text-sm font-medium",
                                                                                        children: item.quantity || 1
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1736,
                                                                                        columnNumber: 33
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                        onClick: ()=>handleIncreaseQuantity(item.productName),
                                                                                        size: "small",
                                                                                        className: "text-green-500 hover:bg-green-50",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Add$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                            fontSize: "small"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                                            lineNumber: 1746,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1739,
                                                                                        columnNumber: 33
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                                lineNumber: 1726,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1724,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1698,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-right",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-sm text-gray-600",
                                                                            children: [
                                                                                "₹",
                                                                                item.price,
                                                                                " ",
                                                                                item.weight_flag === 1 ? '/kg' : '/item'
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1753,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-lg font-semibold text-gray-900",
                                                                            children: [
                                                                                "₹",
                                                                                (item.price * (item.weight_flag === 1 ? item.weight : item.quantity)).toFixed(2)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1756,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1752,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1697,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1664,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1662,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1661,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1609,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-xl shadow-lg p-6 flex flex-col",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-xl font-bold text-gray-800 mb-6",
                                        children: "Order Summary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1775,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide",
                                                children: "Quick Actions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1779,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 gap-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: handleClick,
                                                        variant: "contained",
                                                        color: "primary",
                                                        size: "large",
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faShoppingCart"]
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1786,
                                                            columnNumber: 30
                                                        }, void 0),
                                                        className: "w-full",
                                                        children: "Scan Items"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1781,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: ()=>setOpenSearchDialog(true),
                                                        variant: "outlined",
                                                        size: "large",
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1795,
                                                            columnNumber: 30
                                                        }, void 0),
                                                        className: "w-full",
                                                        children: "Search Products"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1791,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: ()=>setOpenAddProductDialog(true),
                                                        variant: "outlined",
                                                        size: "large",
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddOutlined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1804,
                                                            columnNumber: 30
                                                        }, void 0),
                                                        className: "w-full",
                                                        children: "Add Custom Item"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1800,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: addDemoItems,
                                                        variant: "outlined",
                                                        size: "large",
                                                        className: "w-full",
                                                        color: "secondary",
                                                        children: "Add Demo Items"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1809,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1780,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1778,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide",
                                                children: "Order Details"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1823,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-3 mb-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-sm",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-gray-600",
                                                                children: "Items in cart:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1826,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-medium",
                                                                children: cartItems.length
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1827,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1825,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-sm",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-gray-600",
                                                                children: "Subtotal:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1830,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-medium",
                                                                children: [
                                                                    "₹",
                                                                    subtotal
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1831,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1829,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "border-t pt-3",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-between text-lg font-bold",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: "Total:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1835,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-blue-600",
                                                                    children: Number(totalAmount).toLocaleString("en-IN", {
                                                                        style: "currency",
                                                                        currency: "INR",
                                                                        minimumFractionDigits: 0,
                                                                        maximumFractionDigits: 3
                                                                    })
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1836,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1834,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1833,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1824,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1822,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                variant: "contained",
                                                color: "success",
                                                size: "large",
                                                className: "w-full",
                                                disabled: cartItems.length === 0,
                                                children: "Proceed to Checkout"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1851,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                onClick: handleClearCart,
                                                variant: "outlined",
                                                color: "error",
                                                size: "large",
                                                className: "w-full",
                                                disabled: cartItems.length === 0,
                                                children: "Clear Cart"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1860,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1850,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-6 pt-4 border-t text-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500",
                                            children: "Powered by Synecx AI"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1874,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1873,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1774,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1607,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__["Dialog"], {
                        open: openSearchDialog,
                        onClose: ()=>setOpenSearchDialog(false),
                        fullWidth: true,
                        maxWidth: "sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__["DialogTitle"], {
                                children: [
                                    "Search Products",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        edge: "end",
                                        color: "inherit",
                                        onClick: ()=>setOpenSearchDialog(false),
                                        "aria-label": "close",
                                        sx: {
                                            position: "absolute",
                                            right: 10,
                                            top: 10
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1895,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1888,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1886,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__["DialogContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                        fullWidth: true,
                                        variant: "outlined",
                                        placeholder: "Search for a product...",
                                        value: searchQuery,
                                        onChange: handleInputChange,
                                        sx: {
                                            mb: 2
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1899,
                                        columnNumber: 13
                                    }, this),
                                    displayedItems.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$List$2f$List$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__["List"], {
                                        children: displayedItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__["ListItem"], {
                                                button: true,
                                                onClick: ()=>{
                                                    handleAddToCart(item);
                                                    setOpenSearchDialog(false);
                                                },
                                                sx: {
                                                    display: "flex",
                                                    justifyContent: "space-between",
                                                    borderBottom: "1px solid #ddd",
                                                    padding: "8px"
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                                        children: item.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1924,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                                        sx: {
                                                            fontWeight: "semibold",
                                                            color: "#000000",
                                                            mt: 2
                                                        },
                                                        children: [
                                                            "₹",
                                                            item.price
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1925,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1910,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1908,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                        sx: {
                                            textAlign: "center",
                                            mt: 2,
                                            color: "gray"
                                        },
                                        children: "No products found."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1938,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1898,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1880,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__["Dialog"], {
                        open: openAddProductDialog,
                        onClose: handleCloseAddProductDialog,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__["DialogTitle"], {
                                children: "Add a Product"
                            }, void 0, false, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1951,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__["DialogContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                        id: "productName",
                                        variant: "outlined",
                                        label: "Product Name",
                                        value: newProductName,
                                        onChange: (e)=>setNewProductName(e.target.value),
                                        sx: {
                                            mt: 2,
                                            width: "100%"
                                        },
                                        required: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1953,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                        id: "productPrice",
                                        variant: "outlined",
                                        label: "Product Price",
                                        value: newProductPrice,
                                        onChange: (e)=>setNewProductPrice(e.target.value),
                                        sx: {
                                            mt: 2,
                                            width: "100%"
                                        },
                                        required: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1962,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1952,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogActions$2f$DialogActions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogActions$3e$__["DialogActions"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                        onClick: handleCloseAddProductDialog,
                                        children: "Cancel"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1973,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                        onClick: handleAddProductSubmit,
                                        color: "primary",
                                        variant: "contained",
                                        children: "Add Product"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1974,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/cart/cart.jsx",
                                lineNumber: 1972,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1947,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                        ref: canvasRef,
                        style: {
                            display: "none"
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1985,
                        columnNumber: 9
                    }, this),
                    showMotionToast && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50",
                        children: "Motion Detected!"
                    }, void 0, false, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1989,
                        columnNumber: 11
                    }, this),
                    showItemToast && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50",
                        children: "Item placed"
                    }, void 0, false, {
                        fileName: "[project]/src/components/cart/cart.jsx",
                        lineNumber: 1994,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/cart/cart.jsx",
                lineNumber: 1606,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/cart/cart.jsx",
        lineNumber: 1604,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Cart;
}}),
"[project]/src/components/payment/qr.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const Qr = ({ totalPrice })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col h-screen w-full p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-grow flex flex-col justify-center items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        src: "/QR.svg",
                        className: "h-64 w-64 -mb-10",
                        alt: "QR Code"
                    }, void 0, false, {
                        fileName: "[project]/src/components/payment/qr.tsx",
                        lineNumber: 12,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-12 text-xl font-bold",
                        children: [
                            "Total Price: ₹",
                            totalPrice.toFixed(2)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/payment/qr.tsx",
                        lineNumber: 13,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/payment/qr.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center text-gray-600 mt-40",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-sm font-medium",
                    children: [
                        "Experience the convenience of our ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-bold text-blue-500",
                            children: "AI-powered self-checkout"
                        }, void 0, false, {
                            fileName: "[project]/src/components/payment/qr.tsx",
                            lineNumber: 19,
                            columnNumber: 45
                        }, this),
                        "—fast, secure, and hassle-free."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/payment/qr.tsx",
                    lineNumber: 18,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/payment/qr.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/payment/qr.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Qr;
}}),
"[project]/src/components/payment/payment-animation.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const PaymentAnimation = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " h-full w-full flex justify-center items-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                    src: "/paysuccess.mp4",
                    autoPlay: true,
                    loop: true,
                    muted: true,
                    className: "w-[1000px] h-[600px] object-fit"
                }, void 0, false, {
                    fileName: "[project]/src/components/payment/payment-animation.tsx",
                    lineNumber: 7,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/payment/payment-animation.tsx",
                lineNumber: 6,
                columnNumber: 6
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-12 -mb-24 text-center text-gray-600",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-sm font-medium",
                    children: [
                        "Experience the convenience of our ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-bold text-blue-500",
                            children: "AI-powered self-checkout"
                        }, void 0, false, {
                            fileName: "[project]/src/components/payment/payment-animation.tsx",
                            lineNumber: 18,
                            columnNumber: 45
                        }, this),
                        "—fast, secure, and hassle-free."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/payment/payment-animation.tsx",
                    lineNumber: 17,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/payment/payment-animation.tsx",
                lineNumber: 16,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/payment/payment-animation.tsx",
        lineNumber: 5,
        columnNumber: 4
    }, this);
};
const __TURBOPACK__default__export__ = PaymentAnimation;
}}),
"[project]/src/components/payment/final-payment.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// "use client";
// import React, { useState, useEffect } from "react";
// import Qr from "./qr";
// import PaymentAnimation from "./payment-animation";
// interface FinalPaymentProps {
//   totalPrice: number;
// }
// const FinalPayment: React.FC<FinalPaymentProps> = ({ totalPrice }) => {
//   const [showQr, setShowQr] = useState(true);
//   useEffect(() => {
//     const timer = setTimeout(() => {
//       setShowQr(false);
//     }, 8000);
//     return () => clearTimeout(timer);
//   }, []);
//   return (
//     <div className="flex justify-center items-center h-full w-auto ">
//       {showQr ? <Qr totalPrice={totalPrice} /> : <PaymentAnimation />}
//     </div>
//   );
// };
// export default FinalPayment;
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$qr$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/payment/qr.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$payment$2d$animation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/payment/payment-animation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ads$2f$checkout$2d$ad$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ads/checkout-ad.tsx [app-ssr] (ecmascript)"); // Import CheckoutAd directly
"use client";
;
;
;
;
;
const FinalPayment = ({ totalPrice })=>{
    const [showQr, setShowQr] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [showPaymentAnimation, setShowPaymentAnimation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCheckoutAd, setShowCheckoutAd] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // First transition: Show QR for 8 seconds, then show Payment Animation
        const qrTimer = setTimeout(()=>{
            setShowQr(false);
            setShowPaymentAnimation(true);
        }, 8000);
        // Second transition: Show Payment Animation for 3 seconds, then show CheckoutAd
        const paymentTimer = setTimeout(()=>{
            setShowPaymentAnimation(false);
            setShowCheckoutAd(true);
            // Clear cart items from localStorage
            localStorage.removeItem("cartItems");
            // Reload the page after data is cleared
            setTimeout(()=>{
                window.location.reload();
            }, 500); // Small delay to ensure smooth transition
        }, 11000); // 11 seconds total (8s QR + 3s animation)
        return ()=>{
            clearTimeout(qrTimer);
            clearTimeout(paymentTimer);
        };
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex justify-center items-center h-full w-auto",
        children: [
            showQr && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$qr$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                totalPrice: totalPrice
            }, void 0, false, {
                fileName: "[project]/src/components/payment/final-payment.tsx",
                lineNumber: 74,
                columnNumber: 18
            }, this),
            showPaymentAnimation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$payment$2d$animation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/payment/final-payment.tsx",
                lineNumber: 75,
                columnNumber: 32
            }, this),
            showCheckoutAd && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ads$2f$checkout$2d$ad$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                totalPrice: totalPrice
            }, void 0, false, {
                fileName: "[project]/src/components/payment/final-payment.tsx",
                lineNumber: 76,
                columnNumber: 26
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/payment/final-payment.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = FinalPayment;
}}),
"[project]/src/components/ads/checkout-ad.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$final$2d$payment$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/payment/final-payment.tsx [app-ssr] (ecmascript)"); // Adjust the import path as needed
"use client";
;
;
;
const CheckoutAd = ({ totalPrice })=>{
    const gifs = [
        {
            src: "/diary milk.gif",
            name: "Dairy Milk"
        },
        {
            src: "/icecreamV.jpg",
            name: "Vanilla Ice Cream"
        },
        {
            src: "/cake.jpg",
            name: "Choco Truffle Cake"
        },
        {
            src: "/brownie.jpg",
            name: "Chocolate Brownie"
        },
        {
            src: "/rama.jpg",
            name: "Rasamalai"
        }
    ];
    const [currentGifIndex, setCurrentGifIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [showFinalPayment, setShowFinalPayment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const interval = setInterval(()=>{
            setCurrentGifIndex((prevIndex)=>(prevIndex + 1) % gifs.length);
        }, 5000); // Change GIF every 5 seconds
        return ()=>clearInterval(interval);
    }, [
        gifs.length
    ]);
    // const handleAddProduct = () => {
    //   const currentProduct = gifs[currentGifIndex].name;
    //   alert(`Added ${currentProduct} to the cart!`);
    // };
    const handleContinue = ()=>{
        setShowFinalPayment(true); // Show FinalPayment component
    };
    if (showFinalPayment) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$payment$2f$final$2d$payment$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            totalPrice: totalPrice
        }, void 0, false, {
            fileName: "[project]/src/components/ads/checkout-ad.tsx",
            lineNumber: 43,
            columnNumber: 12
        }, this); // Render FinalPayment
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "h-[700px] w-full bg-white rounded-xl flex flex-col justify-between items-center p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-grow w-full flex justify-center items-center bg-white rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: gifs[currentGifIndex].src,
                    className: "h-[660px] object-contain rounded-xl"
                }, void 0, false, {
                    fileName: "[project]/src/components/ads/checkout-ad.tsx",
                    lineNumber: 50,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ads/checkout-ad.tsx",
                lineNumber: 49,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 flex justify-center gap-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleContinue,
                    className: "bg-blue-500 text-white px-6 py-3 rounded-lg text-lg font-bold hover:bg-gray-600",
                    children: "PROCEED"
                }, void 0, false, {
                    fileName: "[project]/src/components/ads/checkout-ad.tsx",
                    lineNumber: 59,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ads/checkout-ad.tsx",
                lineNumber: 57,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ads/checkout-ad.tsx",
        lineNumber: 47,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = CheckoutAd;
}}),
"[project]/src/components/checkout-final/checkout-final.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$cart$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/cart/cart.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ads$2f$checkout$2d$ad$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ads/checkout-ad.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Final = ()=>{
    const [totalPrice, setTotalPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const handleTotalPriceUpdate = (price)=>{
        setTotalPrice(price);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col md:flex-row gap-4 p-4 h-screen overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 md:basis-2/5 h-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$cart$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    onUpdateTotalPrice: handleTotalPriceUpdate
                }, void 0, false, {
                    fileName: "[project]/src/components/checkout-final/checkout-final.tsx",
                    lineNumber: 16,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/checkout-final/checkout-final.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 md:basis-3/4 h-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ads$2f$checkout$2d$ad$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    totalPrice: totalPrice
                }, void 0, false, {
                    fileName: "[project]/src/components/checkout-final/checkout-final.tsx",
                    lineNumber: 21,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/checkout-final/checkout-final.tsx",
                lineNumber: 20,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/checkout-final/checkout-final.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Final;
}}),
"[project]/src/components/ads/sleep-screen-ad.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$checkout$2d$final$2f$checkout$2d$final$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/checkout-final/checkout-final.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
const SleepScreenAd = ()=>{
    const gifs = [
        "/synecxai.png",
        "/add.png"
    ];
    const [currentGifIndex, setCurrentGifIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [showNewComponent, setShowNewComponent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [slideUp, setSlideUp] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const interval = setInterval(()=>{
            setCurrentGifIndex((prevIndex)=>(prevIndex + 1) % gifs.length);
        }, 5000);
        return ()=>clearInterval(interval);
    }, [
        gifs.length
    ]);
    const handleClick = ()=>{
        setSlideUp(true);
        setTimeout(()=>{
            setShowNewComponent(true);
        }, 500);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$checkout$2d$final$2f$checkout$2d$final$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/ads/sleep-screen-ad.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this),
            !showNewComponent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `absolute top-0 left-0 w-full h-screen transition-transform duration-500 ${slideUp ? "-translate-y-full" : "translate-y-0"}`,
                style: {
                    backgroundImage: `url(${gifs[currentGifIndex]})`,
                    backgroundSize: "100% 100%",
                    backgroundPosition: "center",
                    zIndex: 50
                },
                onClick: handleClick,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-lg font-bold bg-black bg-opacity-50 px-4 py-2 rounded",
                    children: "Tap to Start"
                }, void 0, false, {
                    fileName: "[project]/src/components/ads/sleep-screen-ad.tsx",
                    lineNumber: 51,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ads/sleep-screen-ad.tsx",
                lineNumber: 39,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = SleepScreenAd;
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__472788._.js.map